{"name": "sistem-absensi", "version": "1.0.0", "description": "Sistem Absensi dengan Database Neon", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "pg": "^8.11.3"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["attendance", "system", "neon", "postgresql"], "author": "Your Name", "license": "MIT"}