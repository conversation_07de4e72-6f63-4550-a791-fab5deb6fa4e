:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --text-color: #1f2937;
    --text-light: #6b7280;
    --background: #f3f4f6;
}

body {
    background-color: var(--background);
    background-image: 
        radial-gradient(at 47% 33%, rgba(79, 70, 229, 0.1) 0, transparent 59%), 
        radial-gradient(at 82% 65%, rgba(66, 153, 225, 0.1) 0, transparent 55%);
    min-height: 100vh;
    margin: 0;
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    animation: fadeIn 0.6s ease-out;
}

.logo-container {
    text-align: center;
    margin-bottom: 2rem;
}

.logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo i {
    font-size: 2.5rem;
    color: white;
}

h1 {
    color: var(--text-color);
    font-size: 1.75rem;
    margin: 0 0 0.5rem;
}

.subtitle {
    color: var(--text-light);
    margin: 0;
    font-size: 1rem;
}

.login-form {
    margin-top: 2rem;
}

.input-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.input-group input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.input-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.password-group {
    position: relative;
}

#togglePassword {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0;
    font-size: 1rem;
    transition: color 0.3s ease;
}

#togglePassword:hover {
    color: var(--primary-color);
}

.options {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    color: var(--text-light);
}

.options label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.options input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    border-radius: 4px;
    border: 2px solid #e5e7eb;
    appearance: none;
    cursor: pointer;
}

.options input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.login-btn {
    width: 100%;
    padding: 0.875rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.login-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}

.login-btn:active {
    transform: translateY(0);
}

.message {
    text-align: center;
    margin: 1rem 0;
    padding: 0.75rem;
    border-radius: 8px;
    font-weight: 500;
}

.message.error {
    background-color: #fee2e2;
    color: #dc2626;
}

.message.success {
    background-color: #dcfce7;
    color: #16a34a;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 480px) {
    .login-container {
        margin: 1rem;
        padding: 1.5rem;
    }

    h1 {
        font-size: 1.5rem;
    }

    .logo {
        width: 60px;
        height: 60px;
    }

    .logo i {
        font-size: 2rem;
    }
}
