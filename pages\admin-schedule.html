<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manajemen Jadwal - Sistem Absensi</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/dashboard.css">
    <link rel="stylesheet" href="../assets/admin-pages.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-user-clock"></i>
                </div>
                <h2>Sistem Absensi</h2>
            </div>
            <nav class="sidebar-nav">
                <a href="admin-dashboard.html">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
                <a href="admin-employees.html">
                    <i class="fas fa-users"></i>
                    <span>Karyawan</span>
                </a>
                <a href="admin-attendance.html">
                    <i class="fas fa-clock"></i>
                    <span>Kehadiran</span>
                </a>
                <a href="admin-schedule.html" class="active">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Jadwal</span>
                </a>
                <a href="admin-reports.html">
                    <i class="fas fa-file-alt"></i>
                    <span>Laporan</span>
                </a>
                <a href="admin-settings.html">
                    <i class="fas fa-cog"></i>
                    <span>Pengaturan</span>
                </a>
                <a href="admin-profile.html">
                    <i class="fas fa-user"></i>
                    <span>Profil</span>
                </a>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.html">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="page-title">
                    <h1>Manajemen Jadwal</h1>
                </div>
                <div class="top-bar-right">
                    <div class="date-picker">
                        <input type="month" id="scheduleMonth" aria-label="Pilih Bulan">
                    </div>
                    <button class="add-button" onclick="showModal('addScheduleModal')">
                        <i class="fas fa-plus"></i>
                        <span>Tambah Jadwal</span>
                    </button>
                </div>
            </header>

            <!-- Schedule Management Tabs -->
            <div class="tabs-container">
                <nav class="tabs">
                    <button type="button" class="tab active" data-tab="shiftSchedule">
                        <span>Jadwal Shift</span>
                    </button>
                    <button type="button" class="tab" data-tab="workRules">
                        <span>Aturan Kerja</span>
                    </button>
                    <button type="button" class="tab" data-tab="holidays">
                        <span>Hari Libur</span>
                    </button>
                </nav>
            </div>

            <!-- Schedule Content -->
            <div id="shiftSchedule" class="tab-content active">
                <!-- Schedule Controls -->
                <div class="schedule-controls">
                    <div class="filter-section">
                        <div class="filter-group">
                            <label>Departemen:</label>
                            <select>
                                <option value="">Semua Departemen</option>
                                <option value="it">IT</option>
                                <option value="hr">HR</option>
                                <option value="finance">Finance</option>
                                <option value="marketing">Marketing</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Shift:</label>
                            <select>
                                <option value="">Semua Shift</option>
                                <option value="morning">Pagi</option>
                                <option value="afternoon">Siang</option>
                                <option value="night">Malam</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <button class="filter-button">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Schedule Calendar -->
                <div class="calendar-container">
                    <div class="calendar-header">
                        <div class="calendar-navigation">
                            <button class="nav-btn"><i class="fas fa-chevron-left"></i></button>
                            <h2>Juni 2025</h2>
                            <button class="nav-btn"><i class="fas fa-chevron-right"></i></button>
                        </div>
                    </div>
                    <div class="calendar-grid">
                        <div class="calendar-days">
                            <div>Minggu</div>
                            <div>Senin</div>
                            <div>Selasa</div>
                            <div>Rabu</div>
                            <div>Kamis</div>
                            <div>Jumat</div>
                            <div>Sabtu</div>
                        </div>
                        <div class="calendar-dates">
                            <!-- Calendar cells will be generated by JavaScript -->
                        </div>
                    </div>
                </div>                <!-- Shift Summary -->
                <div class="shift-summary">
                    <div class="summary-header">
                        <h3>Ringkasan Shift Hari Ini</h3>
                        <div class="summary-date">
                            <i class="fas fa-calendar-day"></i>
                            <span>19 Juni 2025</span>
                        </div>
                    </div>
                    <div class="shift-cards">
                        <div class="shift-card morning-shift">
                            <div class="shift-card-header">
                                <div class="shift-badge morning">
                                    <i class="fas fa-sun"></i>
                                    <span>Pagi</span>
                                </div>
                                <div class="shift-time">
                                    <i class="fas fa-clock"></i>
                                    08:00 - 16:00
                                </div>
                            </div>
                            <div class="shift-card-body">
                                <div class="employee-count">
                                    <span class="count">42</span>
                                    <span class="label">Karyawan</span>
                                </div>
                                <div class="shift-actions">
                                    <button class="btn-icon" title="Lihat Detail">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon" title="Edit Shift">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="shift-card afternoon-shift">
                            <div class="shift-card-header">
                                <div class="shift-badge afternoon">
                                    <i class="fas fa-cloud-sun"></i>
                                    <span>Siang</span>
                                </div>
                                <div class="shift-time">
                                    <i class="fas fa-clock"></i>
                                    16:00 - 00:00
                                </div>
                            </div>
                            <div class="shift-card-body">
                                <div class="employee-count">
                                    <span class="count">35</span>
                                    <span class="label">Karyawan</span>
                                </div>
                                <div class="shift-actions">
                                    <button class="btn-icon" title="Lihat Detail">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon" title="Edit Shift">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="shift-card night-shift">
                            <div class="shift-card-header">
                                <div class="shift-badge night">
                                    <i class="fas fa-moon"></i>
                                    <span>Malam</span>
                                </div>
                                <div class="shift-time">
                                    <i class="fas fa-clock"></i>
                                    00:00 - 08:00
                                </div>
                            </div>
                            <div class="shift-card-body">
                                <div class="employee-count">
                                    <span class="count">28</span>
                                    <span class="label">Karyawan</span>
                                </div>
                                <div class="shift-actions">
                                    <button class="btn-icon" title="Lihat Detail">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn-icon" title="Edit Shift">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Work Rules Content -->
            <div id="workRules" class="tab-content">
                <div class="work-rules-container">
                    <!-- Work Rules Cards -->
                    <div class="rules-grid">
                        <!-- Default Work Hours -->
                        <div class="rule-card">
                            <div class="rule-header">
                                <i class="fas fa-clock"></i>
                                <h3>Jam Kerja Standar</h3>
                            </div>
                            <div class="rule-content">
                                <div class="rule-item">
                                    <label>Jam Mulai</label>
                                    <div class="time-display">08:00</div>
                                </div>
                                <div class="rule-item">
                                    <label>Jam Selesai</label>
                                    <div class="time-display">17:00</div>
                                </div>
                                <div class="rule-item">
                                    <label>Toleransi Keterlambatan</label>
                                    <div class="time-display">15 menit</div>
                                </div>
                            </div>
                            <div class="rule-actions">
                                <button class="btn secondary-outline" onclick="editWorkRule('standard')">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </button>
                            </div>
                        </div>

                        <!-- Shift Schedules -->
                        <div class="rule-card">
                            <div class="rule-header">
                                <i class="fas fa-exchange-alt"></i>
                                <h3>Jadwal Shift</h3>
                            </div>
                            <div class="rule-content">
                                <div class="shift-list">
                                    <div class="shift-rule">
                                        <div class="shift-badge morning">Pagi</div>
                                        <div class="shift-time">08:00 - 16:00</div>
                                    </div>
                                    <div class="shift-rule">
                                        <div class="shift-badge afternoon">Siang</div>
                                        <div class="shift-time">16:00 - 00:00</div>
                                    </div>
                                    <div class="shift-rule">
                                        <div class="shift-badge night">Malam</div>
                                        <div class="shift-time">00:00 - 08:00</div>
                                    </div>
                                </div>
                            </div>
                            <div class="rule-actions">
                                <button class="btn secondary-outline" onclick="editWorkRule('shifts')">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </button>
                            </div>
                        </div>

                        <!-- Working Days -->
                        <div class="rule-card">
                            <div class="rule-header">
                                <i class="fas fa-calendar-week"></i>
                                <h3>Hari Kerja</h3>
                            </div>
                            <div class="rule-content">
                                <div class="workdays-grid">
                                    <div class="workday active">S</div>
                                    <div class="workday active">S</div>
                                    <div class="workday active">R</div>
                                    <div class="workday active">K</div>
                                    <div class="workday active">J</div>
                                    <div class="workday">S</div>
                                    <div class="workday">M</div>
                                </div>
                            </div>
                            <div class="rule-actions">
                                <button class="btn secondary-outline" onclick="editWorkRule('workdays')">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </button>
                            </div>
                        </div>

                        <!-- Overtime Rules -->
                        <div class="rule-card">
                            <div class="rule-header">
                                <i class="fas fa-hourglass-half"></i>
                                <h3>Aturan Lembur</h3>
                            </div>
                            <div class="rule-content">
                                <div class="rule-item">
                                    <label>Batas Maksimal</label>
                                    <div class="time-display">4 jam/hari</div>
                                </div>
                                <div class="rule-item">
                                    <label>Kompensasi</label>
                                    <div class="time-display">1.5x gaji per jam</div>
                                </div>
                            </div>
                            <div class="rule-actions">
                                <button class="btn secondary-outline" onclick="editWorkRule('overtime')">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </button>
                            </div>
                        </div>

                        <!-- Break Time Rules -->
                        <div class="rule-card">
                            <div class="rule-header">
                                <i class="fas fa-coffee"></i>
                                <h3>Waktu Istirahat</h3>
                            </div>
                            <div class="rule-content">
                                <div class="rule-item">
                                    <label>Durasi</label>
                                    <div class="time-display">60 menit</div>
                                </div>
                                <div class="rule-item">
                                    <label>Jam Istirahat</label>
                                    <div class="time-display">12:00 - 13:00</div>
                                </div>
                            </div>
                            <div class="rule-actions">
                                <button class="btn secondary-outline" onclick="editWorkRule('break')">
                                    <i class="fas fa-edit"></i>
                                    Edit
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>            <!-- Holidays Content -->
            <div id="holidays" class="tab-content">
                <div class="holidays-container">
                    <div class="holidays-header">
                        <h3>Hari Libur Nasional Tahun 2025</h3>
                        <div class="holidays-actions">
                            <button class="btn secondary" onclick="exportHolidays()">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                            <button class="btn primary" onclick="showModal('addHolidayModal')">
                                <i class="fas fa-plus"></i>
                                Tambah Libur
                            </button>
                        </div>
                    </div>
                    <div class="holidays-table-wrapper">
                        <table class="holidays-table">
                            <thead>
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Hari</th>
                                    <th>Hari Libur</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1 Januari</td>
                                    <td>Rabu</td>
                                    <td>Tahun Baru 2025 Masehi</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>27 Januari</td>
                                    <td>Senin</td>
                                    <td>Isra Mikraj Nabi Muhammad SAW</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>29 Januari</td>
                                    <td>Rabu</td>
                                    <td>Tahun Baru Imlek 2576 Kongzili</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>29 Maret</td>
                                    <td>Sabtu</td>
                                    <td>Hari Suci Nyepi Tahun Baru Saka 1947</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>31 Maret</td>
                                    <td>Senin</td>
                                    <td>Hari Raya Idul Fitri 1446 Hijriyah</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>1 April</td>
                                    <td>Selasa</td>
                                    <td>Hari Raya Idul Fitri 1446 Hijriyah</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>18 April</td>
                                    <td>Jumat</td>
                                    <td>Wafat Yesus Kristus</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>20 April</td>
                                    <td>Minggu</td>
                                    <td>Kebangkitan Yesus Kristus (Paskah)</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>1 Mei</td>
                                    <td>Kamis</td>
                                    <td>Hari Buruh Internasional</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>12 Mei</td>
                                    <td>Senin</td>
                                    <td>Hari Raya Waisak 2569 BE</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>29 Mei</td>
                                    <td>Kamis</td>
                                    <td>Kenaikan Yesus Kristus</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>1 Juni</td>
                                    <td>Minggu</td>
                                    <td>Hari Lahir Pancasila</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>6 Juni</td>
                                    <td>Jumat</td>
                                    <td>Hari Raya Idul Adha 1446 Hijriyah</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>27 Juni</td>
                                    <td>Jumat</td>
                                    <td>Tahun Baru Islam 1447 Hijriyah</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>17 Agustus</td>
                                    <td>Minggu</td>
                                    <td>Hari Kemerdekaan Republik Indonesia</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>5 September</td>
                                    <td>Jumat</td>
                                    <td>Maulid Nabi Muhammad SAW</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>25 Desember</td>
                                    <td>Kamis</td>
                                    <td>Hari Raya Natal</td>
                                    <td class="actions">
                                        <button class="btn-icon" title="Edit"><i class="fas fa-edit"></i></button>
                                        <button class="btn-icon" title="Hapus"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Add Schedule Modal -->
    <div id="addScheduleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Tambah Jadwal Shift</h2>
                <button class="close-btn" onclick="hideModal('addScheduleModal')">&times;</button>
            </div>
            <form class="modal-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>Departemen</label>
                        <select required>
                            <option value="">Pilih Departemen</option>
                            <option value="it">IT</option>
                            <option value="hr">HR</option>
                            <option value="finance">Finance</option>
                            <option value="marketing">Marketing</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Shift</label>
                        <select required>
                            <option value="">Pilih Shift</option>
                            <option value="morning">Pagi (08:00 - 16:00)</option>
                            <option value="afternoon">Siang (16:00 - 00:00)</option>
                            <option value="night">Malam (00:00 - 08:00)</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Tanggal Mulai</label>
                        <input type="date" required>
                    </div>
                    <div class="form-group">
                        <label>Tanggal Selesai</label>
                        <input type="date" required>
                    </div>
                </div>
                <div class="form-group">
                    <label>Karyawan</label>
                    <div class="employee-select">
                        <div class="selected-employees">
                            <!-- Selected employees will be shown here -->
                        </div>
                        <button type="button" class="btn secondary" onclick="showModal('selectEmployeesModal')">
                            <i class="fas fa-plus"></i> Pilih Karyawan
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label>Catatan</label>
                    <textarea rows="3"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn secondary" onclick="hideModal('addScheduleModal')">Batal</button>
                    <button type="submit" class="btn primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Work Rule Modal -->
    <div id="addWorkRuleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Tambah Aturan Kerja</h2>
                <button class="close-btn" onclick="hideModal('addWorkRuleModal')">&times;</button>
            </div>
            <form class="modal-form">
                <div class="form-group">
                    <label>Jenis Aturan</label>
                    <select required>
                        <option value="">Pilih Jenis Aturan</option>
                        <option value="workHours">Jam Kerja</option>
                        <option value="shift">Shift Kerja</option>
                        <option value="overtime">Lembur</option>
                        <option value="break">Istirahat</option>
                    </select>
                </div>
                <div id="ruleFields">
                    <!-- Dynamic fields will be loaded based on rule type -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn secondary" onclick="hideModal('addWorkRuleModal')">Batal</button>
                    <button type="submit" class="btn primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Holiday Modal -->
    <div id="addHolidayModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Tambah Hari Libur</h2>
                <button class="close-btn" onclick="hideModal('addHolidayModal')">&times;</button>
            </div>
            <form class="modal-form" id="holidayForm">
                <div class="form-row">
                    <div class="form-group">
                        <label>Tanggal</label>
                        <input type="date" name="holidayDate" required>
                    </div>
                </div>
                <div class="form-group">
                    <label>Nama Hari Libur</label>
                    <input type="text" name="holidayName" required placeholder="Masukkan nama hari libur">
                </div>
                <div class="form-group">
                    <label>Catatan (Opsional)</label>
                    <textarea name="holidayNotes" rows="3" placeholder="Tambahkan catatan jika diperlukan"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn secondary" onclick="hideModal('addHolidayModal')">Batal</button>
                    <button type="submit" class="btn primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>    <!-- Other existing modals -->
    
    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="../assets/dashboard.js"></script>
    <script src="../assets/admin-pages.js"></script>
    <script src="../assets/schedule.js"></script>
    <script>
        // Initialize month picker with current month
        document.addEventListener('DOMContentLoaded', function() {
            const monthPicker = document.getElementById('scheduleMonth');
            
            // Set default value to current month and year
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            monthPicker.value = `${year}-${month}`;
            
            // Add change event listener
            monthPicker.addEventListener('change', function() {
                // Handle month change - you can add your logic here
                console.log('Selected month:', this.value);
                // Update calendar or fetch data for selected month
            });
        });
    </script>
</body>
</html>
