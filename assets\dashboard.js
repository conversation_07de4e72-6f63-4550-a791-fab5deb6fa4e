// Update current time
function updateTime() {
    const now = new Date();
    const timeElement = document.getElementById('currentTime');
    const dateElement = document.getElementById('currentDate');
    
    if (timeElement) {
        timeElement.textContent = now.toLocaleTimeString('id-ID', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    }
    
    if (dateElement) {
        const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        dateElement.textContent = now.toLocaleDateString('id-ID', options);
    }
}

// Update time every second if we're on the employee dashboard
if (document.getElementById('currentTime')) {
    updateTime();
    setInterval(updateTime, 1000);
}

// Notification dropdown toggle
const notificationIcon = document.querySelector('.notifications');
const notificationDropdown = document.getElementById('notificationDropdown');
if (notificationIcon && notificationDropdown) {
    notificationIcon.addEventListener('click', function(event) {
        event.stopPropagation();
        const isOpen = notificationDropdown.style.display === 'block';
        notificationDropdown.style.display = isOpen ? 'none' : 'block';
    });
    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!notificationIcon.contains(event.target)) {
            notificationDropdown.style.display = 'none';
        }
    });
}

// Profile dropdown toggle
const profileElement = document.querySelector('.admin-profile');
if (profileElement) {
    profileElement.addEventListener('click', function() {
        // Add profile dropdown functionality here
        console.log('Profile clicked');
    });
}

// Search functionality
const searchInput = document.querySelector('.search-bar input');
if (searchInput) {
    searchInput.addEventListener('input', function(e) {
        // Add search functionality here
        console.log('Searching for:', e.target.value);
    });
}

// Chart placeholders (to be replaced with actual chart library implementation)
function initializeMockCharts() {
    // Add actual chart initialization here
    console.log('Charts initialized');
}

// Initialize the dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeMockCharts();
    
    // Add smooth fade-in animation to stats cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
