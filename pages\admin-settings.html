<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengaturan - Sistem Absensi</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/dashboard.css">
    <link rel="stylesheet" href="../assets/admin-pages.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-user-clock"></i>
                </div>
                <h2>Sistem Absensi</h2>
            </div>
            <nav class="sidebar-nav">
                <a href="admin-dashboard.html">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
                <a href="admin-employees.html">
                    <i class="fas fa-users"></i>
                    <span>Karyawan</span>
                </a>
                <a href="admin-attendance.html">
                    <i class="fas fa-clock"></i>
                    <span>Kehadiran</span>
                </a>
                <a href="admin-schedule.html">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Jadwal</span>
                </a>
                <a href="admin-reports.html">
                    <i class="fas fa-file-alt"></i>
                    <span>Laporan</span>
                </a>
                <a href="admin-settings.html" class="active">
                    <i class="fas fa-cog"></i>
                    <span>Pengaturan</span>
                </a>
                <a href="admin-profile.html">
                    <i class="fas fa-user"></i>
                    <span>Profil</span>
                </a>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.html">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="page-title">
                    <h1>Pengaturan Sistem</h1>
                </div>
            </header>

            <form id="settingsForm" class="settings-container">
                <!-- Notification Settings Section -->
                <section class="settings-section settings-card">
                    <div class="settings-header">
                        <i class="fas fa-bell"></i>
                        <h2>Pengaturan Notifikasi</h2>
                    </div>
                    <div class="settings-body">
                        <div class="notification-group">
                            <div class="notification-item">
                                <div class="notification-content">
                                    <h3>Email Notifikasi</h3>
                                    <p>Kirim notifikasi melalui email</p>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" name="email_notification" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            
                            <div class="notification-item">
                                <div class="notification-content">
                                    <h3>Laporan Harian</h3>
                                    <p>Kirim laporan kehadiran harian</p>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" name="daily_report" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            
                            <div class="notification-item">
                                <div class="notification-content">
                                    <h3>Notifikasi Keterlambatan</h3>
                                    <p>Notifikasi untuk karyawan terlambat</p>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" name="late_notification" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Working Hours Settings Section -->
                <section class="settings-section settings-card">
                    <div class="settings-header">
                        <i class="fas fa-clock"></i>
                        <h2>Pengaturan Jam Kerja</h2>
                    </div>
                    <div class="settings-body">
                        <div class="time-settings">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>Jam Kerja Mulai</label>
                                    <input type="time" name="work_start" value="08:00" class="time-input">
                                </div>
                                <div class="form-group">
                                    <label>Jam Kerja Selesai</label>
                                    <input type="time" name="work_end" value="17:00" class="time-input">
                                </div>
                                <div class="form-group">
                                    <label>Toleransi Keterlambatan (menit)</label>
                                    <input type="number" name="late_tolerance" value="15" min="0" max="60" class="number-input">
                                </div>
                                <div class="form-group">
                                    <label>Batas Check-in (menit)</label>
                                    <input type="number" name="checkin_limit" value="120" min="0" class="number-input">
                                </div>
                            </div>
                        </div>
                        
                        <div class="workdays-settings mt-4">
                            <label class="workdays-label">Hari Kerja</label>
                            <div class="workdays-group">
                                <label class="workday-checkbox">
                                    <input type="checkbox" name="workdays[]" value="monday" checked>
                                    <span>Senin</span>
                                </label>
                                <label class="workday-checkbox">
                                    <input type="checkbox" name="workdays[]" value="tuesday" checked>
                                    <span>Selasa</span>
                                </label>
                                <label class="workday-checkbox">
                                    <input type="checkbox" name="workdays[]" value="wednesday" checked>
                                    <span>Rabu</span>
                                </label>
                                <label class="workday-checkbox">
                                    <input type="checkbox" name="workdays[]" value="thursday" checked>
                                    <span>Kamis</span>
                                </label>
                                <label class="workday-checkbox">
                                    <input type="checkbox" name="workdays[]" value="friday" checked>
                                    <span>Jumat</span>
                                </label>
                                <label class="workday-checkbox">
                                    <input type="checkbox" name="workdays[]" value="saturday">
                                    <span>Sabtu</span>
                                </label>
                                <label class="workday-checkbox">
                                    <input type="checkbox" name="workdays[]" value="sunday">
                                    <span>Minggu</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- System Settings Section -->
                <section class="settings-section settings-card">
                    <div class="settings-header">
                        <i class="fas fa-cog"></i>
                        <h2>Pengaturan Sistem</h2>
                    </div>
                    <div class="settings-body">
                        <div class="form-grid">
                            <div class="form-group">
                                <label>Zona Waktu</label>
                                <select name="timezone">
                                    <option value="asia_jakarta">Asia/Jakarta (WIB)</option>
                                    <option value="asia_makassar">Asia/Makassar (WITA)</option>
                                    <option value="asia_jayapura">Asia/Jayapura (WIT)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Format Tanggal</label>
                                <select name="date_format">
                                    <option value="dd_mm_yyyy">DD/MM/YYYY</option>
                                    <option value="mm_dd_yyyy">MM/DD/YYYY</option>
                                    <option value="yyyy_mm_dd">YYYY/MM/DD</option>
                                </select>
                            </div>
                        </div>                        <div class="backup-section">
                            <div class="backup-header">
                                <h3>Backup Database</h3>
                                <p>Pengaturan backup otomatis sistem</p>
                            </div>
                            <div class="backup-controls">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="backup_schedule">Jadwal Backup</label>
                                        <select name="backup_schedule" id="backup_schedule" class="backup-select">
                                            <option value="daily">Setiap Hari</option>
                                            <option value="weekly">Setiap Minggu</option>
                                            <option value="monthly">Setiap Bulan</option>
                                        </select>
                                    </div>
                                    <div class="form-group backup-button-group">
                                        <label class="invisible">Backup Manual</label>
                                        <button type="button" class="btn primary-outline backup-button" onclick="backupDatabase()">
                                            <i class="fas fa-download"></i>
                                            <span>Backup Sekarang</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Save Button -->
                <div class="bottom-actions">
                    <button type="submit" class="btn primary save-all">
                        <i class="fas fa-save"></i>
                        Simpan Perubahan
                    </button>
                </div>
            </form>
        </main>
    </div>

    <script src="../assets/dashboard.js"></script>    <script src="../assets/admin-pages.js"></script>
    <script src="../assets/settings.js"></script>
</body>
</html>
