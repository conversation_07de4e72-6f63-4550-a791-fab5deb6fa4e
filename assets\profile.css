/* Profile Page Specific Styles */

/* Profile Content */
.profile-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Profile Header Card */
.profile-header-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.profile-cover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    height: 120px;
    position: relative;
    display: flex;
    align-items: flex-end;
    padding: 0 2rem 1rem;
}

.profile-avatar-large {
    position: relative;
    margin-bottom: -40px;
}

.profile-avatar-large img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.avatar-edit-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 3px solid white;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.avatar-edit-btn:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.profile-info {
    padding: 3rem 2rem 2rem;
}

.profile-info h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.employee-id {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.position {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.profile-stats {
    display: flex;
    gap: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.stat-item {
    text-align: center;
}

.stat-item .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
}

.stat-item .stat-label {
    font-size: 0.85rem;
    color: var(--text-light);
}

/* Profile Sections */
.profile-sections {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.profile-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    background: #fafafa;
}

.section-header h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
}

.section-header h3 i {
    color: var(--primary-color);
}

.edit-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.edit-btn:hover {
    background: var(--primary-hover);
}

.section-content {
    padding: 2rem;
}

/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item label {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--text-light);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item p {
    font-size: 1rem;
    color: var(--text-color);
    font-weight: 500;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 999px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: #dcfce7;
    color: #166534;
}

/* Settings List */
.settings-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info h4 {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.setting-info p {
    font-size: 0.9rem;
    color: var(--text-light);
}

.setting-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--background);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.setting-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: var(--background);
    color: var(--text-color);
}

/* Form Styles */
.password-form {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.btn-secondary {
    padding: 0.75rem 1.5rem;
    background: var(--background);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-primary {
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: var(--primary-hover);
}

/* Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem;
    background: var(--background);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: var(--text-color);
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}

.quick-action-btn i {
    font-size: 1.5rem;
    color: var(--primary-color);
    transition: color 0.3s ease;
}

.quick-action-btn:hover i {
    color: white;
}

.quick-action-btn span {
    font-weight: 500;
    font-size: 0.9rem;
}

/* Admin-specific styling */
.admin-cover {
    background: linear-gradient(135deg, var(--primary-hover), #3730a3) !important;
}

.admin-cover .avatar-edit-btn {
    background: var(--primary-hover) !important;
}

.admin-cover .avatar-edit-btn:hover {
    background: #3730a3 !important;
}

/* Admin form styling */
.password-form small {
    color: var(--text-light);
    font-size: 0.8rem;
    margin-top: 0.25rem;
    display: block;
}

/* Admin settings section */
.settings-list .setting-item {
    border-left: 3px solid transparent;
    padding-left: 1.5rem;
    transition: border-color 0.3s ease;
}

.settings-list .setting-item:hover {
    border-left-color: var(--primary-color);
    background: #f8fafc;
}

/* Admin select styling */
.settings-list select {
    border: 1px solid var(--border-color);
    transition: border-color 0.3s ease;
}

.settings-list select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .profile-info {
        padding: 2rem 1rem 1.5rem;
    }

    .section-header,
    .section-content {
        padding: 1rem;
    }

    .profile-cover {
        padding: 0 1rem 1rem;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
}