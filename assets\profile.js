// Profile Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize profile page functionality
    initializeProfilePage();
});

function initializeProfilePage() {
    // Determine user role from URL or localStorage
    const userRole = determineUserRole();
    
    // Initialize based on user role
    if (userRole === 'admin') {
        initializeAdminProfile();
    } else {
        initializeEmployeeProfile();
    }
    
    // Initialize common functionality
    initializeCommonFeatures();
}

function determineUserRole() {
    // Check if user came from admin pages
    const referrer = document.referrer;
    if (referrer.includes('admin-')) {
        return 'admin';
    }
    
    // Check localStorage for user role
    const storedRole = localStorage.getItem('userRole');
    if (storedRole) {
        return storedRole;
    }
    
    // Default to employee
    return 'employee';
}

function initializeAdminProfile() {
    // Set admin-specific content
    document.getElementById('userName').textContent = 'Administrator';
    document.getElementById('userId').textContent = 'ADM001';
    document.getElementById('userPosition').textContent = 'Administrator';
    document.getElementById('fullName').textContent = 'Administrator';
    document.getElementById('userEmail').textContent = '<EMAIL>';
    document.getElementById('jobTitle').textContent = 'Administrator';
    document.getElementById('department').textContent = 'IT Management';
    document.getElementById('supervisor').textContent = 'CEO';
    
    // Show admin-specific section
    document.getElementById('adminSection').style.display = 'block';
    
    // Update profile stats for admin
    const profileStats = document.getElementById('profileStats');
    profileStats.innerHTML = `
        <div class="stat-item">
            <span class="stat-number">156</span>
            <span class="stat-label">Total Karyawan</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">142</span>
            <span class="stat-label">Hadir Hari Ini</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">91%</span>
            <span class="stat-label">Tingkat Kehadiran</span>
        </div>
    `;
    
    // Update user profile image
    const userProfileImg = document.querySelector('#userProfile img');
    if (userProfileImg) {
        userProfileImg.src = 'https://ui-avatars.com/api/?name=Admin&background=4f46e5&color=fff';
    }
    
    // Set admin navigation
    setAdminNavigation();
    
    // Store role in localStorage
    localStorage.setItem('userRole', 'admin');
}

function initializeEmployeeProfile() {
    // Set employee-specific content
    document.getElementById('userName').textContent = 'John Doe';
    document.getElementById('userId').textContent = 'EMP001';
    document.getElementById('userPosition').textContent = 'Software Developer';
    document.getElementById('fullName').textContent = 'John Doe';
    document.getElementById('userEmail').textContent = '<EMAIL>';
    document.getElementById('jobTitle').textContent = 'Software Developer';
    document.getElementById('department').textContent = 'IT Development';
    document.getElementById('supervisor').textContent = 'Jane Smith';
    
    // Update profile stats for employee
    const profileStats = document.getElementById('profileStats');
    profileStats.innerHTML = `
        <div class="stat-item">
            <span class="stat-number">22</span>
            <span class="stat-label">Hari Hadir</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">164</span>
            <span class="stat-label">Jam Kerja</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">10</span>
            <span class="stat-label">Sisa Cuti</span>
        </div>
    `;
    
    // Update user profile image
    const userProfileImg = document.querySelector('#userProfile img');
    if (userProfileImg) {
        userProfileImg.src = 'https://ui-avatars.com/api/?name=John+Doe&background=4f46e5&color=fff';
    }
    
    // Set employee navigation
    setEmployeeNavigation();
    
    // Store role in localStorage
    localStorage.setItem('userRole', 'employee');
}

function setAdminNavigation() {
    const sidebarNav = document.getElementById('sidebarNav');
    sidebarNav.innerHTML = `
        <a href="admin-dashboard.html">
            <i class="fas fa-home"></i>
            <span>Dashboard</span>
        </a>
        <a href="admin-employees.html">
            <i class="fas fa-users"></i>
            <span>Karyawan</span>
        </a>
        <a href="admin-attendance.html">
            <i class="fas fa-clock"></i>
            <span>Kehadiran</span>
        </a>
        <a href="admin-schedule.html">
            <i class="fas fa-calendar-alt"></i>
            <span>Jadwal</span>
        </a>
        <a href="admin-reports.html">
            <i class="fas fa-file-alt"></i>
            <span>Laporan</span>
        </a>
        <a href="admin-settings.html">
            <i class="fas fa-cog"></i>
            <span>Pengaturan</span>
        </a>
        <a href="admin-profile.html" class="active">
            <i class="fas fa-user"></i>
            <span>Profil</span>
        </a>
    `;
}

function setEmployeeNavigation() {
    const sidebarNav = document.getElementById('sidebarNav');
    sidebarNav.innerHTML = `
        <a href="employee-dashboard.html">
            <i class="fas fa-home"></i>
            <span>Dashboard</span>
        </a>
        <a href="#">
            <i class="fas fa-clock"></i>
            <span>Absensi</span>
        </a>
        <a href="employee-schedule.html">
            <i class="fas fa-calendar-alt"></i>
            <span>Jadwal</span>
        </a>
        <a href="employee-reports.html">
            <i class="fas fa-file-alt"></i>
            <span>Laporan Saya</span>
        </a>
        <a href="employee-requests.html">
            <i class="fas fa-envelope"></i>
            <span>Pengajuan</span>
        </a>
        <a href="employee-profile.html" class="active">
            <i class="fas fa-user"></i>
            <span>Profil</span>
        </a>
    `;
}

function initializeCommonFeatures() {
    // Modal elements
    const passwordModal = document.getElementById('passwordModal');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const closePasswordModal = document.getElementById('closePasswordModal');
    const cancelPassword = document.getElementById('cancelPassword');
    const passwordForm = document.getElementById('passwordForm');
    
    // Edit buttons
    const editPersonalBtn = document.getElementById('editPersonalBtn');
    const changeAvatarBtn = document.getElementById('changeAvatarBtn');
    
    // Event listeners for modals
    if (changePasswordBtn) {
        changePasswordBtn.addEventListener('click', () => {
            openModal(passwordModal);
        });
    }
    
    if (closePasswordModal) {
        closePasswordModal.addEventListener('click', () => {
            closeModal(passwordModal);
        });
    }
    
    if (cancelPassword) {
        cancelPassword.addEventListener('click', () => {
            closeModal(passwordModal);
        });
    }
    
    // Close modal when clicking outside
    if (passwordModal) {
        passwordModal.addEventListener('click', (e) => {
            if (e.target === passwordModal) {
                closeModal(passwordModal);
            }
        });
    }
    
    // Password form submission
    if (passwordForm) {
        passwordForm.addEventListener('submit', handlePasswordChange);
    }
    
    // Edit personal info button
    if (editPersonalBtn) {
        editPersonalBtn.addEventListener('click', handleEditPersonal);
    }
    
    // Change avatar button
    if (changeAvatarBtn) {
        changeAvatarBtn.addEventListener('click', handleChangeAvatar);
    }
    
    // Initialize tooltips and other UI enhancements
    initializeUIEnhancements();
}

// Modal functions
function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        
        // Focus on first input if exists
        const firstInput = modal.querySelector('input');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100);
        }
    }
}

function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
        
        // Reset form if exists
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
        }
    }
}

// Password change handler
function handlePasswordChange(e) {
    e.preventDefault();
    
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // Validation
    if (!currentPassword || !newPassword || !confirmPassword) {
        showNotification('Semua field harus diisi', 'error');
        return;
    }
    
    if (newPassword.length < 8) {
        showNotification('Password baru minimal 8 karakter', 'error');
        return;
    }
    
    if (newPassword !== confirmPassword) {
        showNotification('Konfirmasi password tidak cocok', 'error');
        return;
    }
    
    // Simulate API call
    showNotification('Mengubah password...', 'info');
    
    setTimeout(() => {
        // Simulate success
        showNotification('Password berhasil diubah!', 'success');
        closeModal(document.getElementById('passwordModal'));
        
        // Reset form
        document.getElementById('passwordForm').reset();
    }, 2000);
}

// Edit personal info handler
function handleEditPersonal() {
    // This would typically open an edit form or modal
    // For now, we'll show a notification
    showNotification('Fitur edit informasi pribadi akan segera hadir!', 'info');
}

// Change avatar handler
function handleChangeAvatar() {
    // Create file input
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.style.display = 'none';
    
    fileInput.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                showNotification('Ukuran file terlalu besar. Maksimal 5MB', 'error');
                return;
            }
            
            // Validate file type
            if (!file.type.startsWith('image/')) {
                showNotification('File harus berupa gambar', 'error');
                return;
            }
            
            // Preview image
            const reader = new FileReader();
            reader.onload = function(e) {
                const avatarImg = document.querySelector('.profile-avatar-large img');
                const userProfileImg = document.querySelector('#userProfile img');
                if (avatarImg) {
                    avatarImg.src = e.target.result;
                }
                if (userProfileImg) {
                    userProfileImg.src = e.target.result;
                }
                showNotification('Foto profil berhasil diubah!', 'success');
            };
            reader.readAsDataURL(file);
        }
    });
    
    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
}

// UI Enhancements
function initializeUIEnhancements() {
    // Add loading states to buttons
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.classList.contains('btn-primary') || this.classList.contains('edit-btn')) {
                addLoadingState(this);
            }
        });
    });
    
    // Add hover effects to cards
    const cards = document.querySelectorAll('.profile-section, .profile-header-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
        });
    });
}

// Utility functions
function addLoadingState(button) {
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    button.disabled = true;
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 2000);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        max-width: 400px;
        animation: slideInRight 0.3s ease;
    `;
    
    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex: 1;
        }
        
        .notification-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: background 0.3s ease;
        }
        
        .notification-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    `;
    document.head.appendChild(style);
    
    // Add to page
    document.body.appendChild(notification);
    
    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.remove();
    });
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'warning': return 'fa-exclamation-triangle';
        default: return 'fa-info-circle';
    }
}

function getNotificationColor(type) {
    switch (type) {
        case 'success': return '#16a34a';
        case 'error': return '#dc2626';
        case 'warning': return '#ca8a04';
        default: return '#4f46e5';
    }
}

// Add slideOutRight animation
const slideOutStyle = document.createElement('style');
slideOutStyle.textContent = `
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(slideOutStyle); 