// Report page specific functionality
function generateReport(type) {
    // Show loading state
    const reportCard = document.querySelector(`[data-report-type="${type}"]`);
    if (reportCard) {
        const button = reportCard.querySelector('.btn.primary');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';
        button.disabled = true;

        // Simulate report generation
        setTimeout(() => {
            button.innerHTML = '<i class="fas fa-check"></i> Complete!';
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                
                // Add to recent reports
                addRecentReport(type);
            }, 1500);
        }, 2000);
    }
}

function addRecentReport(type) {
    const tbody = document.querySelector('.recent-reports tbody');
    if (tbody) {
        const now = new Date();
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>Report ${type.charAt(0).toUpperCase() + type.slice(1)} - ${now.toLocaleDateString()}</td>
            <td>${now.toLocaleString()}</td>
            <td>${now.toLocaleDateString()} - ${now.toLocaleDateString()}</td>
            <td>PDF</td>
            <td><span class="status-badge active">Selesai</span></td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="downloadReport('${type}')">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="action-btn delete" onclick="deleteReport(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.insertBefore(row, tbody.firstChild);
        
        // Add fade-in animation
        row.style.opacity = '0';
        row.style.animation = 'fadeIn 0.5s forwards';
    }
}

function downloadReport(type) {
    // Implement download functionality
    console.log(`Downloading ${type} report...`);
}

function deleteReport(button) {
    const row = button.closest('tr');
    row.style.animation = 'fadeOut 0.5s forwards';
    setTimeout(() => row.remove(), 500);
}

// Add animation keyframes
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(10px); }
    }
`;
document.head.appendChild(style);

// Initialize tooltips and other UI enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to report cards
    const reportCards = document.querySelectorAll('.report-card');
    reportCards.forEach(card => {
        card.addEventListener('mouseover', function() {
            this.style.transform = 'translateY(-5px)';
        });
        card.addEventListener('mouseout', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
