// Report page specific functionality
window.generateReport = function(type) {
    console.log(`Generating ${type} report...`);

    try {
        // Show report configuration modal first
        showReportModal(type);
    } catch (error) {
        console.error('Error showing modal:', error);
        // Fallback: direct report generation
        generateQuickReport(type);
    }
}

// Fallback function for quick report generation
function generateQuickReport(type) {
    const reportData = generateSampleData(type, 'month');
    generatePDFReport(reportData, type, 'month');
    showNotification(`Laporan ${getReportTitle(type)} berhasil dibuat!`, 'success');
}

function showReportModal(reportType) {
    // Create modal if it doesn't exist
    let modal = document.getElementById('reportModal');
    if (!modal) {
        createReportModal();
        modal = document.getElementById('reportModal');
    }

    // Update modal content based on report type
    updateModalContent(reportType);

    // Show modal
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function createReportModal() {
    const modalHTML = `
        <div id="reportModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modalTitle">Buat Laporan</h2>
                    <button class="close-btn" onclick="hideReportModal()">&times;</button>
                </div>
                <div class="modal-form">
                    <div class="form-group">
                        <label for="reportPeriod">Periode Laporan</label>
                        <select id="reportPeriod" required>
                            <option value="today">Hari Ini</option>
                            <option value="week">Minggu Ini</option>
                            <option value="month" selected>Bulan Ini</option>
                            <option value="quarter">Kuartal Ini</option>
                            <option value="year">Tahun Ini</option>
                            <option value="custom">Periode Kustom</option>
                        </select>
                    </div>
                    <div id="customDateRange" class="form-row" style="display: none;">
                        <div class="form-group">
                            <label for="startDate">Tanggal Mulai</label>
                            <input type="date" id="startDate">
                        </div>
                        <div class="form-group">
                            <label for="endDate">Tanggal Selesai</label>
                            <input type="date" id="endDate">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="reportFormat">Format Laporan</label>
                        <select id="reportFormat" required>
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel (.xlsx)</option>
                            <option value="csv">CSV</option>
                        </select>
                    </div>
                    <div id="reportOptions" class="form-group">
                        <!-- Dynamic options based on report type -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn secondary" onclick="hideReportModal()">Batal</button>
                    <button type="button" class="btn primary" onclick="processReport()">
                        <i class="fas fa-file-export"></i>
                        Buat Laporan
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add event listener for period change
    document.getElementById('reportPeriod').addEventListener('change', function() {
        const customRange = document.getElementById('customDateRange');
        if (this.value === 'custom') {
            customRange.style.display = 'flex';
        } else {
            customRange.style.display = 'none';
        }
    });
}

function updateModalContent(reportType) {
    const modalTitle = document.getElementById('modalTitle');
    const reportOptions = document.getElementById('reportOptions');

    // Update title
    const titles = {
        'attendance': 'Buat Laporan Kehadiran',
        'performance': 'Buat Laporan Performa',
        'department': 'Buat Laporan Departemen',
        'overtime': 'Buat Laporan Lembur'
    };
    modalTitle.textContent = titles[reportType] || 'Buat Laporan';

    // Update specific options based on report type
    let optionsHTML = '';

    switch(reportType) {
        case 'attendance':
            optionsHTML = `
                <label>Opsi Laporan Kehadiran</label>
                <div class="checkbox-group">
                    <label><input type="checkbox" value="late" checked> Termasuk Keterlambatan</label>
                    <label><input type="checkbox" value="absent" checked> Termasuk Ketidakhadiran</label>
                    <label><input type="checkbox" value="overtime" checked> Termasuk Lembur</label>
                    <label><input type="checkbox" value="summary" checked> Ringkasan Statistik</label>
                </div>
            `;
            break;
        case 'performance':
            optionsHTML = `
                <label>Opsi Laporan Performa</label>
                <div class="checkbox-group">
                    <label><input type="checkbox" value="individual" checked> Performa Individual</label>
                    <label><input type="checkbox" value="comparison" checked> Perbandingan Antar Karyawan</label>
                    <label><input type="checkbox" value="trends" checked> Tren Performa</label>
                    <label><input type="checkbox" value="charts" checked> Grafik dan Visualisasi</label>
                </div>
            `;
            break;
        case 'department':
            optionsHTML = `
                <label>Pilih Departemen</label>
                <select id="departmentSelect" multiple>
                    <option value="all" selected>Semua Departemen</option>
                    <option value="it">IT</option>
                    <option value="hr">HR</option>
                    <option value="finance">Finance</option>
                    <option value="marketing">Marketing</option>
                    <option value="operations">Operations</option>
                </select>
                <div class="checkbox-group" style="margin-top: 1rem;">
                    <label><input type="checkbox" value="summary" checked> Ringkasan per Departemen</label>
                    <label><input type="checkbox" value="comparison" checked> Perbandingan Departemen</label>
                </div>
            `;
            break;
        case 'overtime':
            optionsHTML = `
                <label>Opsi Laporan Lembur</label>
                <div class="checkbox-group">
                    <label><input type="checkbox" value="hours" checked> Total Jam Lembur</label>
                    <label><input type="checkbox" value="cost" checked> Biaya Lembur</label>
                    <label><input type="checkbox" value="frequency" checked> Frekuensi Lembur</label>
                    <label><input type="checkbox" value="approval" checked> Status Persetujuan</label>
                </div>
            `;
            break;
    }

    reportOptions.innerHTML = optionsHTML;

    // Store current report type
    document.getElementById('reportModal').setAttribute('data-report-type', reportType);
}

window.hideReportModal = function() {
    const modal = document.getElementById('reportModal');
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = '';
    }
}

window.processReport = function() {
    const modal = document.getElementById('reportModal');
    const reportType = modal.getAttribute('data-report-type');
    const period = document.getElementById('reportPeriod').value;
    const format = document.getElementById('reportFormat').value;

    // Get custom date range if selected
    let dateRange = '';
    if (period === 'custom') {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        if (!startDate || !endDate) {
            alert('Silakan pilih tanggal mulai dan selesai untuk periode kustom');
            return;
        }
        dateRange = `${startDate} - ${endDate}`;
    }

    // Get selected options
    const checkboxes = modal.querySelectorAll('input[type="checkbox"]:checked');
    const selectedOptions = Array.from(checkboxes).map(cb => cb.value);

    // Hide modal
    hideReportModal();

    // Show loading and generate report
    showReportProgress(reportType, period, format, dateRange, selectedOptions);
}

function showReportProgress(reportType, period, format, dateRange, options) {
    // Find the button that was clicked
    const buttons = document.querySelectorAll('.report-card .btn.primary');
    let targetButton = null;

    buttons.forEach(button => {
        const onclick = button.getAttribute('onclick');
        if (onclick && onclick.includes(reportType)) {
            targetButton = button;
        }
    });

    if (!targetButton) return;

    // Show loading state
    const originalText = targetButton.innerHTML;
    targetButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Membuat Laporan...';
    targetButton.disabled = true;

    // Simulate report generation
    setTimeout(() => {
        targetButton.innerHTML = '<i class="fas fa-check"></i> Selesai!';

        // Generate and download the report
        generateActualReport(reportType, period, format, dateRange, options);

        setTimeout(() => {
            targetButton.innerHTML = originalText;
            targetButton.disabled = false;
        }, 2000);
    }, 3000);
}

function generateActualReport(reportType, period, format, dateRange, options) {
    console.log('Generating report with parameters:', {
        reportType, period, format, dateRange, options
    });

    // Create sample data based on report type
    const reportData = generateSampleData(reportType, period);

    // Generate file based on format
    switch(format) {
        case 'pdf':
            generatePDFReport(reportData, reportType, period);
            break;
        case 'excel':
            generateExcelReport(reportData, reportType, period);
            break;
        case 'csv':
            generateCSVReport(reportData, reportType, period);
            break;
    }

    // Show success notification
    showNotification(`Laporan ${getReportTitle(reportType)} berhasil dibuat dan diunduh!`, 'success');
}

function generateSampleData(reportType, period) {
    const now = new Date();
    const data = {
        reportType,
        period,
        generatedAt: now.toLocaleString('id-ID'),
        data: []
    };

    switch(reportType) {
        case 'attendance':
            data.data = [
                { nama: 'John Doe', hadir: 22, terlambat: 2, tidak_hadir: 1, lembur: 5 },
                { nama: 'Jane Smith', hadir: 24, terlambat: 0, tidak_hadir: 1, lembur: 3 },
                { nama: 'Ahmad Yani', hadir: 23, terlambat: 1, tidak_hadir: 1, lembur: 4 },
                { nama: 'Siti Nurhaliza', hadir: 25, terlambat: 0, tidak_hadir: 0, lembur: 2 }
            ];
            break;
        case 'performance':
            data.data = [
                { nama: 'John Doe', skor_kehadiran: 95, skor_ketepatan: 90, skor_total: 92.5 },
                { nama: 'Jane Smith', skor_kehadiran: 98, skor_ketepatan: 100, skor_total: 99 },
                { nama: 'Ahmad Yani', skor_kehadiran: 96, skor_ketepatan: 95, skor_total: 95.5 },
                { nama: 'Siti Nurhaliza', skor_kehadiran: 100, skor_ketepatan: 100, skor_total: 100 }
            ];
            break;
        case 'department':
            data.data = [
                { departemen: 'IT', total_karyawan: 15, hadir_rata: 95, terlambat_rata: 5 },
                { departemen: 'HR', total_karyawan: 8, hadir_rata: 98, terlambat_rata: 2 },
                { departemen: 'Finance', total_karyawan: 12, hadir_rata: 97, terlambat_rata: 3 },
                { departemen: 'Marketing', total_karyawan: 10, hadir_rata: 94, terlambat_rata: 6 }
            ];
            break;
        case 'overtime':
            data.data = [
                { nama: 'John Doe', total_jam: 25, total_hari: 8, biaya: 'Rp 2.500.000' },
                { nama: 'Jane Smith', total_jam: 15, total_hari: 5, biaya: 'Rp 1.500.000' },
                { nama: 'Ahmad Yani', total_jam: 20, total_hari: 6, biaya: 'Rp 2.000.000' },
                { nama: 'Siti Nurhaliza', total_jam: 10, total_hari: 3, biaya: 'Rp 1.000.000' }
            ];
            break;
    }

    return data;
}

function generatePDFReport(data, reportType, period) {
    // Simulate PDF generation
    const content = createReportContent(data);
    const blob = new Blob([content], { type: 'text/plain' });
    downloadFile(blob, `laporan_${reportType}_${period}.txt`);
}

function generateExcelReport(data, reportType, period) {
    // Simulate Excel generation (in real implementation, use a library like SheetJS)
    const content = createCSVContent(data);
    const blob = new Blob([content], { type: 'text/csv' });
    downloadFile(blob, `laporan_${reportType}_${period}.csv`);
}

function generateCSVReport(data, reportType, period) {
    const content = createCSVContent(data);
    const blob = new Blob([content], { type: 'text/csv' });
    downloadFile(blob, `laporan_${reportType}_${period}.csv`);
}

function createReportContent(data) {
    let content = `LAPORAN ${getReportTitle(data.reportType).toUpperCase()}\n`;
    content += `Periode: ${data.period}\n`;
    content += `Dibuat pada: ${data.generatedAt}\n`;
    content += `\n${'='.repeat(50)}\n\n`;

    data.data.forEach((item, index) => {
        content += `${index + 1}. `;
        Object.entries(item).forEach(([key, value]) => {
            content += `${key}: ${value}, `;
        });
        content = content.slice(0, -2) + '\n';
    });

    return content;
}

function createCSVContent(data) {
    if (data.data.length === 0) return '';

    const headers = Object.keys(data.data[0]);
    let csv = headers.join(',') + '\n';

    data.data.forEach(row => {
        const values = headers.map(header => row[header]);
        csv += values.join(',') + '\n';
    });

    return csv;
}

function downloadFile(blob, filename) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function getReportTitle(reportType) {
    const titles = {
        'attendance': 'Kehadiran',
        'performance': 'Performa',
        'department': 'Departemen',
        'overtime': 'Lembur'
    };
    return titles[reportType] || reportType;
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">×</button>
    `;

    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : '#3b82f6'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        max-width: 400px;
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function downloadReport(type) {
    // Legacy function for backward compatibility
    console.log(`Downloading ${type} report...`);
    showNotification(`Mengunduh laporan ${getReportTitle(type)}...`, 'info');
}

function deleteReport(button) {
    const row = button.closest('tr');
    row.style.animation = 'fadeOut 0.5s forwards';
    setTimeout(() => row.remove(), 500);
}

// Add animation keyframes and styles
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(10px); }
    }
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    .checkbox-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .checkbox-group label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: normal;
        cursor: pointer;
    }

    .checkbox-group input[type="checkbox"] {
        margin: 0;
    }

    #departmentSelect {
        min-height: 100px;
    }

    .notification button {
        background: none;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 0;
        margin-left: auto;
    }

    .form-row {
        display: flex;
        gap: 1rem;
    }

    .form-row .form-group {
        flex: 1;
    }
`;
document.head.appendChild(style);

// Initialize tooltips and other UI enhancements
document.addEventListener('DOMContentLoaded', function() {
    console.log('Reports page loaded, initializing...');

    // Verify all functions are available
    console.log('generateReport function available:', typeof window.generateReport === 'function');
    console.log('hideReportModal function available:', typeof window.hideReportModal === 'function');
    console.log('processReport function available:', typeof window.processReport === 'function');

    // Add hover effects to report cards
    const reportCards = document.querySelectorAll('.report-card');
    console.log('Found report cards:', reportCards.length);

    reportCards.forEach((card, index) => {
        card.addEventListener('mouseover', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
        });
        card.addEventListener('mouseout', function() {
            this.style.transform = 'translateY(0)';
        });

        // Add click event as backup
        const button = card.querySelector('.btn.primary');
        if (button) {
            console.log(`Setting up button ${index + 1}`);
            button.addEventListener('click', function(e) {
                // This will work even if onclick fails
                const onclickAttr = this.getAttribute('onclick');
                if (onclickAttr) {
                    const match = onclickAttr.match(/generateReport\('(\w+)'\)/);
                    if (match) {
                        e.preventDefault();
                        console.log(`Button clicked for report type: ${match[1]}`);
                        window.generateReport(match[1]);
                    }
                }
            });
        }
    });

    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        const modal = document.getElementById('reportModal');
        if (modal && e.target === modal) {
            hideReportModal();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('reportModal');
            if (modal && modal.classList.contains('show')) {
                hideReportModal();
            }
        }
    });

    console.log('Reports page initialization complete');
});
