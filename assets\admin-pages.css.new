/* Variables */
:root {
    --primary-color: #6366F1;
    --text-color: #1F2937;
    --text-muted: #6B7280;
    --border-color: #E5E7EB;
    --background-light: #F9FAFB;
    --hover-color: #F3F4F6;
    --error-color: #DC2626;
    --success-color: #059669;
}

/* Admin Pages Specific Styles */

/* Page Title */
.page-title {
    margin: 0;
}

.page-title h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

/* Top Bar Styles */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: 12px;   
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);    
    width: 100%;
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* <PERSON><PERSON> Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.btn i {
    font-size: 1rem;
}

.btn.primary {
    background: var(--primary-color);
    color: white;
}

.btn.primary:hover {
    background: var(--primary-color);
    opacity: 0.9;
}

.btn.secondary {
    background: var(--background-light);
    color: var(--text-color);
    border-color: var(--border-color);
}

.btn.secondary:hover {
    background: var(--hover-color);
}

.btn.secondary-outline {
    background: transparent;
    color: var(--text-color);
    border-color: var(--border-color);
}

.btn.secondary-outline:hover {
    background: var(--hover-color);
}

/* Form Styles */
.form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.875rem;
    color: var(--text-color);
    background: #fff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    padding: 20px;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 12px;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.modal-form {
    padding: 24px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.close-btn:hover {
    color: var(--text-color);
}

/* Table Styles */
.table-container {
    background: white;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: var(--background);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.summary-card {
    background: white;
    padding: 1.25rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.summary-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.summary-details h3 {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
}

.summary-details p {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.page-btn {
    min-width: 36px;
    height: 36px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-color);
}

.page-btn:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.page-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.page-btn:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.page-separator {
    color: var(--text-muted);
}

/* Schedule Page Styles */
.tabs-container {
    margin-bottom: 20px;
}

.tabs {
    display: flex;
    gap: 10px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1px;
}

.tab {
    padding: 10px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 1em;
    color: var(--text-muted);
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab:hover {
    color: var(--primary-color);
}

.tab.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Calendar Styles */
.calendar-container {
    background: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.calendar-navigation {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.calendar-navigation h2 {
    font-size: 1.25rem;
    margin: 0;
}

.nav-btn {
    background: var(--background);
    border: none;
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nav-btn:hover {
    background: var(--border-color);
}

.calendar-grid {
    display: grid;
    gap: 1px;
    background: var(--border-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: var(--background-light);
}

.calendar-days div {
    padding: 10px;
    text-align: center;
    font-weight: 600;
    background: #ffffff;
}

.calendar-dates {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.calendar-cell {
    background: #ffffff;
    min-height: 100px;
    padding: 10px;
    position: relative;
}

.calendar-cell.empty {
    background: var(--background-light);
}

.date-number {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.calendar-cell.current .date-number {
    background: var(--primary-color);
    color: #ffffff;
}

/* Shift Indicators */
.shift-indicators {
    margin-top: 25px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.shift-indicator {
    width: 100%;
    height: 4px;
    border-radius: 2px;
}

.shift-indicator.morning {
    background-color: #1976d2;
}

.shift-indicator.afternoon {
    background-color: #f57c00;
}

.shift-indicator.night {
    background-color: #3f51b5;
}

/* Date Picker Styles */
.date-picker-wrapper {
    position: relative;
    width: 100%;
    max-width: 300px;
    display: inline-block;
    padding: 5px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--border-color);
}

.date-picker-input {
    width: 100%;
    min-width: unset;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.875rem;
    color: var(--text-color);
    background-color: #fff;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.date-picker-input:hover {
    border-color: #D1D5DB;
}

.date-picker-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.date-picker-input:disabled {
    background-color: var(--background-light);
    cursor: not-allowed;
    opacity: 0.7;
}

/* Flatpickr Styles */
.flatpickr-wrapper {
    width: 100%;
    max-width: 300px;
    position: relative;
    display: inline-block;
}

.flatpickr-wrapper input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.875rem;
    color: var(--text-color);
    background-color: #fff;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.flatpickr-wrapper input:hover {
    border-color: #D1D5DB;
}

.flatpickr-wrapper input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.flatpickr-calendar {
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 
                0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    border-radius: 8px !important;
    background: #fff !important;
}

.flatpickr-day.selected,
.flatpickr-day.selected:hover {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.flatpickr-day.today {
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
}

.flatpickr-day:hover {
    background: var(--hover-color) !important;
}

/* Work Rules Styles */
.work-rules-container {
    padding: 20px;
}

.rules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.rule-card {
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.rule-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rule-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 12px;
}

.rule-header i {
    font-size: 1.5em;
    color: var(--primary-color);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-light);
    border-radius: 8px;
}

.rule-header h3 {
    margin: 0;
    font-size: 1.2em;
    color: var(--text-color);
    font-weight: 600;
}

.rule-content {
    margin-bottom: 15px;
}

.rule-item {
    margin-bottom: 12px;
}

.rule-item:last-child {
    margin-bottom: 0;
}

.rule-item label {
    display: block;
    font-size: 0.9em;
    color: var(--text-muted);
    margin-bottom: 4px;
}

/* Holidays Styles */
.holidays-container {
    padding: 20px;
}

.holidays-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.holidays-table-wrapper {
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.holidays-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 800px;
}

.holidays-table th,
.holidays-table td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.holidays-table th {
    background: var(--background-light);
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
}

.holidays-table td {
    color: var(--text-color);
    vertical-align: middle;
}

.holidays-table tr:hover {
    background-color: var(--hover-color);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .workdays-group {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }
    
    .backup-controls .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .backup-button-group {
        width: 100%;
    }
    
    .backup-button-group .invisible {
        display: none;
    }
    
    .btn.primary-outline {
        width: 100%;
        justify-content: center;
    }
    
    .schedule-controls-group {
        flex-direction: column;
        width: 100%;
        gap: 0.75rem;
    }
    
    .btn.primary.add-schedule-btn {
        width: 100%;
        justify-content: center;
    }
    
    .date-picker-wrapper,
    .flatpickr-wrapper {
        max-width: 100%;
    }
    
    .rules-grid {
        grid-template-columns: 1fr;
    }
    
    .workdays-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
    }
    
    .holidays-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .holidays-actions {
        justify-content: space-between;
    }
    
    .holidays-actions .btn {
        flex: 1;
    }
    
    .holidays-table-wrapper {
        margin: 0 -20px;
        border-radius: 0;
    }
    
    .shift-summary {
        padding: 16px;
    }
    
    .summary-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .shift-cards {
        grid-template-columns: 1fr;
    }
    
    .shift-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}
