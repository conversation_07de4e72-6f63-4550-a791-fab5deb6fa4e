// Admin Profile Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize admin profile page
    initializeAdminProfile();
    initializeCommonFeatures();
});

function initializeAdminProfile() {
    // Set admin-specific content
    const adminData = {
        name: 'Administrator',
        id: 'ADM001',
        position: 'System Administrator',
        email: '<EMAIL>',
        phone: '+62 811-1234-5678',
        address: 'Jl. Sudirman No. 123, Jakarta Pusat',
        birthDate: '15 Januari 1980',
        gender: 'Laki-laki',
        department: 'IT Management',
        joinDate: '1 Januari 2020',
        accessLevel: 'Super Administrator',
        permissions: 'Full System Access, User Management, Reports'
    };

    // Update profile information
    updateAdminInfo(adminData);
    
    // Update admin stats
    updateAdminStats();
    
    // Store role in localStorage
    localStorage.setItem('userRole', 'admin');
}

function updateAdminInfo(data) {
    // Update header info
    document.getElementById('userName').textContent = data.name;
    document.getElementById('userId').textContent = data.id;
    document.getElementById('userPosition').textContent = data.position;
    
    // Update personal information
    document.getElementById('fullName').textContent = 'Administrator System';
    document.getElementById('userEmail').textContent = data.email;
    document.getElementById('userPhone').textContent = data.phone;
    document.getElementById('userAddress').textContent = data.address;
    document.getElementById('userBirthDate').textContent = data.birthDate;
    document.getElementById('userGender').textContent = data.gender;
    
    // Update administrative information
    document.getElementById('adminId').textContent = data.id;
    document.getElementById('accessLevel').textContent = data.accessLevel;
    document.getElementById('department').textContent = data.department;
    document.getElementById('joinDate').textContent = data.joinDate;
    document.getElementById('permissions').textContent = data.permissions;
    
    // Update last login and IP
    updateLoginInfo();
    
    // Update avatar images
    const avatarUrl = `https://ui-avatars.com/api/?name=Admin&background=4338ca&color=fff`;
    document.querySelectorAll('img[alt="Admin"]').forEach(img => {
        img.src = avatarUrl;
    });
    
    // Update profile name in top bar
    document.querySelector('.admin-profile span').textContent = data.name;
}

function updateAdminStats() {
    // This would typically come from an API
    const stats = {
        totalEmployees: 150,
        departments: 25,
        pendingRequests: 12,
        systemUptime: '98%'
    };
    
    const statElements = document.querySelectorAll('.stat-item');
    if (statElements.length >= 4) {
        statElements[0].querySelector('.stat-number').textContent = stats.totalEmployees;
        statElements[1].querySelector('.stat-number').textContent = stats.departments;
        statElements[2].querySelector('.stat-number').textContent = stats.pendingRequests;
        statElements[3].querySelector('.stat-number').textContent = stats.systemUptime;
    }
}

function updateLoginInfo() {
    const now = new Date();
    const lastLoginElement = document.getElementById('lastLogin');
    const ipAddressElement = document.getElementById('ipAddress');
    
    if (lastLoginElement) {
        lastLoginElement.textContent = 'Hari ini, 07:30 WIB';
    }
    
    if (ipAddressElement) {
        ipAddressElement.textContent = '*************';
    }
}

function initializeCommonFeatures() {
    // Modal elements
    const passwordModal = document.getElementById('passwordModal');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const closePasswordModal = document.getElementById('closePasswordModal');
    const cancelPassword = document.getElementById('cancelPassword');
    const passwordForm = document.getElementById('passwordForm');
    
    // Edit buttons
    const editPersonalBtn = document.getElementById('editPersonalBtn');
    const changeAvatarBtn = document.getElementById('changeAvatarBtn');
    
    // Event listeners for modals
    if (changePasswordBtn) {
        changePasswordBtn.addEventListener('click', () => {
            openModal(passwordModal);
        });
    }
    
    if (closePasswordModal) {
        closePasswordModal.addEventListener('click', () => {
            closeModal(passwordModal);
        });
    }
    
    if (cancelPassword) {
        cancelPassword.addEventListener('click', () => {
            closeModal(passwordModal);
        });
    }
    
    // Password form submission
    if (passwordForm) {
        passwordForm.addEventListener('submit', handleAdminPasswordChange);
    }
    
    // Edit personal info
    if (editPersonalBtn) {
        editPersonalBtn.addEventListener('click', handleEditPersonalInfo);
    }
    
    // Change avatar
    if (changeAvatarBtn) {
        changeAvatarBtn.addEventListener('click', handleChangeAvatar);
    }
    
    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });
}

function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
        
        // Reset form if it's the password modal
        if (modal.id === 'passwordModal') {
            const form = modal.querySelector('form');
            if (form) form.reset();
        }
    }
}

function handleAdminPasswordChange(e) {
    e.preventDefault();
    
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // Validate passwords
    if (newPassword !== confirmPassword) {
        alert('Password baru dan konfirmasi password tidak cocok!');
        return;
    }
    
    if (newPassword.length < 8) {
        alert('Password administrator harus minimal 8 karakter!');
        return;
    }
    
    // Additional validation for admin password
    const hasUpperCase = /[A-Z]/.test(newPassword);
    const hasLowerCase = /[a-z]/.test(newPassword);
    const hasNumbers = /\d/.test(newPassword);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(newPassword);
    
    if (!hasUpperCase || !hasLowerCase || !hasNumbers || !hasSpecialChar) {
        alert('Password administrator harus mengandung huruf besar, huruf kecil, angka, dan simbol!');
        return;
    }
    
    // Here you would typically send the data to your backend
    console.log('Admin password change request:', {
        currentPassword,
        newPassword
    });
    
    // Simulate success
    alert('Password administrator berhasil diubah!');
    closeModal(document.getElementById('passwordModal'));
    
    // Log security event
    logSecurityEvent('Password Changed', 'Administrator password was changed');
}

function handleEditPersonalInfo() {
    // This would open an edit form or make fields editable
    alert('Fitur edit informasi pribadi administrator akan segera tersedia!');
}

function handleChangeAvatar() {
    // This would open a file picker or avatar selection modal
    alert('Fitur ubah avatar administrator akan segera tersedia!');
}

// Admin-specific action handlers
function handleAdminAction(action) {
    switch(action) {
        case 'backup':
            handleBackupData();
            break;
        case 'audit':
            handleAuditLog();
            break;
        default:
            console.log('Unknown admin action:', action);
    }
}

function handleBackupData() {
    if (confirm('Apakah Anda yakin ingin melakukan backup data sistem?')) {
        // Simulate backup process
        alert('Backup data dimulai. Anda akan menerima notifikasi setelah selesai.');
        logSecurityEvent('Data Backup', 'System backup initiated by administrator');
    }
}

function handleAuditLog() {
    // This would open audit log viewer
    alert('Membuka audit log sistem...');
    // In real implementation, this might open a modal or redirect to audit page
    window.location.href = 'admin-settings.html#audit';
}

function logSecurityEvent(eventType, description) {
    // This would typically send to backend for security logging
    console.log('Security Event:', {
        type: eventType,
        description: description,
        timestamp: new Date().toISOString(),
        user: 'Administrator',
        ip: '*************'
    });
}

// System monitoring functions
function updateSystemStats() {
    // This would fetch real-time system statistics
    const systemStats = {
        totalEmployees: 150,
        activeDepartments: 25,
        pendingApprovals: 12,
        systemUptime: 98.5,
        databaseSize: '2.4 GB',
        lastBackup: 'Kemarin, 23:00 WIB'
    };
    
    // Update system management section
    updateSystemManagementInfo(systemStats);
}

function updateSystemManagementInfo(stats) {
    // Update system management statistics
    const systemSection = document.querySelector('.profile-section:nth-child(4)');
    if (systemSection) {
        const infoItems = systemSection.querySelectorAll('.info-item p');
        if (infoItems.length >= 6) {
            infoItems[0].textContent = `${stats.totalEmployees} Karyawan`;
            infoItems[1].textContent = `${stats.activeDepartments} Departemen`;
            infoItems[2].textContent = `${stats.pendingApprovals} Pengajuan`;
            infoItems[3].textContent = `${stats.systemUptime}%`;
            infoItems[4].textContent = stats.databaseSize;
            infoItems[5].textContent = stats.lastBackup;
        }
    }
}

// Initialize system monitoring
setInterval(updateSystemStats, 30000); // Update every 30 seconds

// Export functions for global access
window.handleAdminAction = handleAdminAction;
