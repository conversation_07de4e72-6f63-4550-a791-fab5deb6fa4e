<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manaj<PERSON>en <PERSON>hadiran - Sistem Absensi</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/dashboard.css">
    <link rel="stylesheet" href="../assets/admin-pages.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar (same as admin-employees.html) -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-user-clock"></i>
                </div>
                <h2>Sistem Absensi</h2>
            </div>
            <nav class="sidebar-nav">
                <a href="admin-dashboard.html">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
                <a href="admin-employees.html">
                    <i class="fas fa-users"></i>
                    <span>Karyawan</span>
                </a>
                <a href="admin-attendance.html" class="active">
                    <i class="fas fa-clock"></i>
                    <span>Kehadiran</span>
                </a>
                <a href="admin-schedule.html">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Jadwal</span>
                </a>
                <a href="admin-reports.html">
                    <i class="fas fa-file-alt"></i>
                    <span>Laporan</span>
                </a>
                <a href="admin-settings.html">
                    <i class="fas fa-cog"></i>
                    <span>Pengaturan</span>
                </a>
                <a href="admin-profile.html">
                    <i class="fas fa-user"></i>
                    <span>Profil</span>
                </a>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.html">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="page-title">
                    <h1>Manajemen Kehadiran</h1>
                </div>
                <div class="top-bar-right">
                    <div class="date-picker">
                        <input type="date" id="attendanceDate" aria-label="Pilih Tanggal">
                    </div>
                    <button class="add-button" onclick="showModal('addAttendanceModal')">
                        <i class="fas fa-plus"></i>
                        <span>Tambah Manual</span>
                    </button>
                </div>
            </header>

            <!-- Attendance Summary -->
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-icon" style="background-color: var(--success-color)">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="summary-details">
                        <h3>Hadir</h3>
                        <p>142 Karyawan</p>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon" style="background-color: var(--warning-color)">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="summary-details">
                        <h3>Terlambat</h3>
                        <p>8 Karyawan</p>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon" style="background-color: var(--danger-color)">
                        <i class="fas fa-times"></i>
                    </div>
                    <div class="summary-details">
                        <h3>Tidak Hadir</h3>
                        <p>6 Karyawan</p>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon" style="background-color: var(--primary-color)">
                        <i class="fas fa-house-leave"></i>
                    </div>
                    <div class="summary-details">
                        <h3>Cuti/Izin</h3>
                        <p>4 Karyawan</p>
                    </div>
                </div>
            </div>

            <!-- Attendance Filters -->
            <div class="filter-section">
                <div class="filter-group">
                    <label>Departemen:</label>
                    <select>
                        <option value="">Semua Departemen</option>
                        <option value="it">IT</option>
                        <option value="hr">HR</option>
                        <option value="finance">Finance</option>
                        <option value="marketing">Marketing</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Status:</label>
                    <select>
                        <option value="">Semua Status</option>
                        <option value="present">Hadir</option>
                        <option value="late">Terlambat</option>
                        <option value="absent">Tidak Hadir</option>
                        <option value="leave">Cuti</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button class="filter-button">
                        <i class="fas fa-filter"></i>
                        Filter
                    </button>
                    <button class="filter-button">
                        <i class="fas fa-download"></i>
                        Export
                    </button>
                </div>
            </div>

            <!-- Attendance Table -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID Karyawan</th>
                            <th>Nama</th>
                            <th>Departemen</th>
                            <th>Tanggal</th>
                            <th>Check In</th>
                            <th>Check Out</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>EMP001</td>
                            <td>
                                <div class="employee-info">
                                    <img src="https://ui-avatars.com/api/?name=John+Doe&background=4f46e5&color=fff" alt="John Doe">
                                    <span>John Doe</span>
                                </div>
                            </td>
                            <td>IT</td>
                            <td>19 Juni 2025</td>
                            <td>08:02</td>
                            <td>17:05</td>
                            <td><span class="status-badge present">Hadir</span></td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-btn edit" onclick="showModal('editAttendanceModal')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn view" onclick="showModal('viewAttendanceModal')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <!-- More attendance rows -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination">
                <button class="page-btn" disabled>
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="page-btn active">1</button>
                <button class="page-btn">2</button>
                <button class="page-btn">3</button>
                <span class="page-separator">...</span>
                <button class="page-btn">10</button>
                <button class="page-btn">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </main>
    </div>

    <!-- Add Attendance Modal -->
    <div id="addAttendanceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Tambah Kehadiran Manual</h2>
                <button class="close-btn" onclick="hideModal('addAttendanceModal')">&times;</button>
            </div>
            <form class="modal-form">
                <div class="form-row">
                    <div class="form-group">
                        <label>Karyawan</label>
                        <select required>
                            <option value="">Pilih Karyawan</option>
                            <option value="1">John Doe - EMP001</option>
                            <option value="2">Jane Smith - EMP002</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Tanggal</label>
                        <input type="date" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Waktu Check In</label>
                        <input type="time" required>
                    </div>
                    <div class="form-group">
                        <label>Waktu Check Out</label>
                        <input type="time">
                    </div>
                </div>
                <div class="form-group">
                    <label>Status</label>
                    <select required>
                        <option value="present">Hadir</option>
                        <option value="late">Terlambat</option>
                        <option value="absent">Tidak Hadir</option>
                        <option value="leave">Cuti</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Keterangan</label>
                    <textarea rows="3"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn secondary" onclick="hideModal('addAttendanceModal')">Batal</button>
                    <button type="submit" class="btn primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/dashboard.js"></script>
    <script src="../assets/admin-pages.js"></script>
</body>
</html>
