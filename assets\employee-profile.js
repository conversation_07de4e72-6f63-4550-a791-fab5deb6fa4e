// Employee Profile Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize employee profile page
    initializeEmployeeProfile();
    initializeCommonFeatures();
    updateDateTime();
    setInterval(updateDateTime, 1000);
});

function initializeEmployeeProfile() {
    // Set employee-specific content
    const employeeData = {
        name: '<PERSON>',
        id: 'EMP001',
        position: 'Software Developer',
        email: '<EMAIL>',
        phone: '+62 812-3456-7890',
        address: 'Jl. Merdeka No. 45, Jakarta Selatan',
        birthDate: '25 Agustus 1990',
        gender: 'La<PERSON>-laki',
        department: 'IT Development',
        joinDate: '15 Maret 2022',
        supervisor: '<PERSON> (IT Manager)',
        workingHours: '08:00 - 17:00 WIB',
        workLocation: 'Jakarta Office'
    };

    // Update profile information
    updateProfileInfo(employeeData);
    
    // Update profile stats
    updateProfileStats();
    
    // Store role in localStorage
    localStorage.setItem('userRole', 'employee');
}

function updateProfileInfo(data) {
    // Update header info
    document.getElementById('userName').textContent = data.name;
    document.getElementById('userId').textContent = data.id;
    document.getElementById('userPosition').textContent = data.position;
    
    // Update personal information
    document.getElementById('fullName').textContent = data.name;
    document.getElementById('userEmail').textContent = data.email;
    document.getElementById('userPhone').textContent = data.phone;
    document.getElementById('userAddress').textContent = data.address;
    document.getElementById('userBirthDate').textContent = data.birthDate;
    document.getElementById('userGender').textContent = data.gender;
    
    // Update employment information
    document.getElementById('employeeId').textContent = data.id;
    document.getElementById('jobTitle').textContent = data.position;
    document.getElementById('department').textContent = data.department;
    document.getElementById('joinDate').textContent = data.joinDate;
    document.getElementById('supervisor').textContent = data.supervisor;
    document.getElementById('workingHours').textContent = data.workingHours;
    document.getElementById('workLocation').textContent = data.workLocation;
    
    // Update avatar images
    const avatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(data.name)}&background=4f46e5&color=fff`;
    document.querySelectorAll('img[alt="John Doe"]').forEach(img => {
        img.src = avatarUrl;
        img.alt = data.name;
    });
    
    // Update profile name in top bar
    document.querySelector('.admin-profile span').textContent = data.name;
}

function updateProfileStats() {
    // This would typically come from an API
    const stats = {
        attendanceDays: 22,
        workingHours: 164,
        remainingLeave: 10,
        attendanceRate: '95%'
    };
    
    const statElements = document.querySelectorAll('.stat-item');
    if (statElements.length >= 4) {
        statElements[0].querySelector('.stat-number').textContent = stats.attendanceDays;
        statElements[1].querySelector('.stat-number').textContent = stats.workingHours;
        statElements[2].querySelector('.stat-number').textContent = stats.remainingLeave;
        statElements[3].querySelector('.stat-number').textContent = stats.attendanceRate;
    }
}

function updateDateTime() {
    const now = new Date();
    const timeElement = document.getElementById('currentTime');
    const dateElement = document.getElementById('currentDate');
    
    if (timeElement) {
        timeElement.textContent = now.toLocaleTimeString('id-ID', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
    
    if (dateElement) {
        dateElement.textContent = now.toLocaleDateString('id-ID', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
}

function initializeCommonFeatures() {
    // Modal elements
    const passwordModal = document.getElementById('passwordModal');
    const changePasswordBtn = document.getElementById('changePasswordBtn');
    const closePasswordModal = document.getElementById('closePasswordModal');
    const cancelPassword = document.getElementById('cancelPassword');
    const passwordForm = document.getElementById('passwordForm');
    
    // Edit buttons
    const editPersonalBtn = document.getElementById('editPersonalBtn');
    const changeAvatarBtn = document.getElementById('changeAvatarBtn');
    
    // Event listeners for modals
    if (changePasswordBtn) {
        changePasswordBtn.addEventListener('click', () => {
            openModal(passwordModal);
        });
    }
    
    if (closePasswordModal) {
        closePasswordModal.addEventListener('click', () => {
            closeModal(passwordModal);
        });
    }
    
    if (cancelPassword) {
        cancelPassword.addEventListener('click', () => {
            closeModal(passwordModal);
        });
    }
    
    // Password form submission
    if (passwordForm) {
        passwordForm.addEventListener('submit', handlePasswordChange);
    }
    
    // Edit personal info
    if (editPersonalBtn) {
        editPersonalBtn.addEventListener('click', handleEditPersonalInfo);
    }
    
    // Change avatar
    if (changeAvatarBtn) {
        changeAvatarBtn.addEventListener('click', handleChangeAvatar);
    }
    
    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target);
        }
    });
}

function openModal(modal) {
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modal) {
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
        
        // Reset form if it's the password modal
        if (modal.id === 'passwordModal') {
            const form = modal.querySelector('form');
            if (form) form.reset();
        }
    }
}

function handlePasswordChange(e) {
    e.preventDefault();
    
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // Validate passwords
    if (newPassword !== confirmPassword) {
        alert('Password baru dan konfirmasi password tidak cocok!');
        return;
    }
    
    if (newPassword.length < 6) {
        alert('Password baru harus minimal 6 karakter!');
        return;
    }
    
    // Here you would typically send the data to your backend
    console.log('Password change request:', {
        currentPassword,
        newPassword
    });
    
    // Simulate success
    alert('Password berhasil diubah!');
    closeModal(document.getElementById('passwordModal'));
}

function handleEditPersonalInfo() {
    // This would open an edit form or make fields editable
    alert('Fitur edit informasi pribadi akan segera tersedia!');
}

function handleChangeAvatar() {
    // This would open a file picker or avatar selection modal
    alert('Fitur ubah avatar akan segera tersedia!');
}

// Quick action handlers
function handleQuickAction(action) {
    switch(action) {
        case 'leave':
            window.location.href = 'employee-requests.html?type=leave';
            break;
        case 'late':
            window.location.href = 'employee-requests.html?type=late';
            break;
        case 'report':
            window.location.href = 'employee-reports.html';
            break;
        case 'schedule':
            window.location.href = 'employee-schedule.html';
            break;
        default:
            console.log('Unknown action:', action);
    }
}

// Export functions for global access
window.handleQuickAction = handleQuickAction;
