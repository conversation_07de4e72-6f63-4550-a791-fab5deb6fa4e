# Setup Guide - Sistem Absensi

Panduan lengkap untuk menjalankan aplikasi sistem absensi dengan database Neon PostgreSQL.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Setup Environment Variables
```bash
# Copy example environment file
cp .env.example .env

# Edit .env file dengan kredensial database Neon Anda
```

### 3. Configure Database
Edit file `.env` dengan informasi database Neon:
```env
DB_HOST=your-project.neon.tech
DB_PORT=5432
DB_NAME=neondb
DB_USER=your-username
DB_PASSWORD=your-password
DB_SSL=true
JWT_SECRET=your-secret-key
```

### 4. Initialize Database
```bash
npm run init-db
```

### 5. Start Application
```bash
# Development mode
npm run dev

# Production mode
npm start
```

### 6. Access Application
- **Frontend**: http://localhost:3000
- **API**: http://localhost:3000/api
- **Health Check**: http://localhost:3000/api/health

## 🔐 Default Login Credentials

### Admin
- **Email**: <EMAIL>
- **Password**: admin123

### Employee
- **Email**: <EMAIL>
- **Password**: employee123

## 📋 Database Setup (Neon)

### 1. Create Neon Account
1. Go to https://neon.tech
2. Sign up for free account
3. Create new project

### 2. Get Database Credentials
1. Go to your project dashboard
2. Click "Connection Details"
3. Copy the connection parameters

### 3. Configure Environment
Update `.env` file with your Neon credentials:
```env
DB_HOST=ep-mute-bird-a1882vlw-pooler.ap-southeast-1.aws.neon.tech
DB_PORT=5432
DB_NAME=neondb
DB_USER=neondb_owner
DB_PASSWORD=your-password
DB_SSL=true
```

## 🛠️ Development

### File Structure
```
├── server.js              # Main server file
├── routes/                # API routes
│   ├── auth.js            # Authentication
│   ├── employees.js       # Employee management
│   ├── attendance.js      # Attendance tracking
│   ├── departments.js     # Department management
│   └── reports.js         # Report generation
├── middleware/            # Express middleware
├── config/               # Configuration files
├── assets/               # Frontend JavaScript/CSS
├── pages/                # HTML pages
└── scripts/              # Utility scripts
```

### API Testing
```bash
# Test health endpoint
curl http://localhost:3000/api/health

# Test login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Database Connection Error
```
Error: connect ECONNREFUSED
```
**Solution**: 
- Check Neon database credentials in `.env`
- Ensure database is running
- Verify SSL configuration

#### 2. JWT Secret Error
```
Error: JWT secret not provided
```
**Solution**: 
- Set `JWT_SECRET` in `.env` file
- Use a strong, random secret key

#### 3. Port Already in Use
```
Error: listen EADDRINUSE :::3000
```
**Solution**: 
- Change `PORT` in `.env` file
- Or kill process using port 3000

#### 4. Module Not Found
```
Error: Cannot find module 'express'
```
**Solution**: 
- Run `npm install` to install dependencies
- Check `package.json` for correct dependencies

### Database Issues

#### Reset Database
```bash
# Re-run database initialization
npm run init-db
```

#### Check Database Connection
```bash
# Test database connection
node -e "
const { Pool } = require('pg');
require('dotenv').config();
const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  ssl: process.env.DB_SSL === 'true'
});
pool.query('SELECT NOW()', (err, res) => {
  if (err) console.error('Connection failed:', err);
  else console.log('Connected successfully:', res.rows[0]);
  pool.end();
});
"
```

## 📱 Frontend Features

### Admin Dashboard
- Employee management (CRUD operations)
- Attendance monitoring
- Report generation
- Department management

### Employee Portal
- Personal dashboard
- Check-in/check-out
- View attendance history
- Profile management

## 🔒 Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Rate limiting
- CORS protection
- Security headers with Helmet
- Input validation

## 📊 API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update profile

### Employee Endpoints
- `GET /api/employees` - List employees
- `POST /api/employees` - Create employee
- `GET /api/employees/:id` - Get employee
- `PUT /api/employees/:id` - Update employee
- `DELETE /api/employees/:id` - Delete employee

### Attendance Endpoints
- `GET /api/attendance` - Get attendance records
- `POST /api/attendance/checkin` - Check in
- `POST /api/attendance/checkout` - Check out

## 🚀 Deployment

### Production Checklist
- [ ] Set `NODE_ENV=production`
- [ ] Use strong JWT secret
- [ ] Configure production database
- [ ] Enable HTTPS
- [ ] Set up reverse proxy
- [ ] Configure monitoring

### Environment Variables for Production
```env
NODE_ENV=production
DB_SSL=true
JWT_SECRET=very-secure-production-secret
FRONTEND_URL=https://yourdomain.com
```

## 📞 Support

If you encounter any issues:
1. Check this troubleshooting guide
2. Verify environment configuration
3. Check server logs
4. Test API endpoints manually

For additional help, please refer to the main README.md file.
