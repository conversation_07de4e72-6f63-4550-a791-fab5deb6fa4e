// Ganti dengan native event handler untuk input type="month"
document.addEventListener('DOMContentLoaded', function() {
    const monthInput = document.getElementById('scheduleMonth');
    if (monthInput) {
        // Set default value ke bulan ini
        const now = new Date();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const year = now.getFullYear();
        monthInput.value = `${year}-${month}`;

        // Event handler saat bulan berubah
        monthInput.addEventListener('change', function(e) {
            // TODO: update tampilan kalender sesuai bulan yang dipilih
            // Misal: updateCalendar(e.target.value);
        });
    }
});

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tab functionality
    initializeTabs();
    
    // Initialize date pickers in modal
    const modalDatePickers = document.querySelectorAll('.modal-form input[type="date"]');
    modalDatePickers.forEach(input => {
        // Initialize date picker
        input.addEventListener('change', function(e) {
            validateDateRange();
        });
    });

    // Initialize calendar with current date
    const now = new Date();
    generateCalendar(now.getFullYear(), now.getMonth());
});

// Function to validate date range
function validateDateRange() {
    const startDate = document.querySelector('input[type="date"][required]:nth-of-type(1)');
    const endDate = document.querySelector('input[type="date"][required]:nth-of-type(2)');
    
    if (startDate && endDate && startDate.value && endDate.value) {
        if (new Date(startDate.value) > new Date(endDate.value)) {
            endDate.value = startDate.value;
        }
    }
}

// Calendar functionality
function generateCalendar(year, month) {
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const calendarDates = document.querySelector('.calendar-dates');
    const monthNames = ['Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 
                       'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'];

    // Update calendar header
    document.querySelector('.calendar-navigation h2').textContent = `${monthNames[month]} ${year}`;

    // Clear existing calendar dates
    calendarDates.innerHTML = '';

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay.getDay(); i++) {
        const emptyCell = document.createElement('div');
        emptyCell.className = 'calendar-cell empty';
        calendarDates.appendChild(emptyCell);
    }

    // Add cells for each day of the month
    for (let day = 1; day <= lastDay.getDate(); day++) {
        const cell = document.createElement('div');
        cell.className = 'calendar-cell';
        
        // Add date number
        const dateNumber = document.createElement('div');
        dateNumber.className = 'date-number';
        dateNumber.textContent = day;
        cell.appendChild(dateNumber);
        
        // Add shift indicators (example)
        const shiftsContainer = document.createElement('div');
        shiftsContainer.className = 'shift-indicators';
        
        // Add sample shift indicators (you would replace this with actual data)
        if (Math.random() > 0.5) {
            const morningShift = document.createElement('div');
            morningShift.className = 'shift-indicator morning';
            morningShift.title = 'Shift Pagi';
            shiftsContainer.appendChild(morningShift);
        }
        if (Math.random() > 0.5) {
            const afternoonShift = document.createElement('div');
            afternoonShift.className = 'shift-indicator afternoon';
            afternoonShift.title = 'Shift Siang';
            shiftsContainer.appendChild(afternoonShift);
        }
        if (Math.random() > 0.7) {
            const nightShift = document.createElement('div');
            nightShift.className = 'shift-indicator night';
            nightShift.title = 'Shift Malam';
            shiftsContainer.appendChild(nightShift);
        }
        
        cell.appendChild(shiftsContainer);
        
        // Highlight current day
        const currentDate = new Date();
        if (currentDate.getDate() === day && 
            currentDate.getMonth() === month && 
            currentDate.getFullYear() === year) {
            cell.classList.add('current');
        }
        
        calendarDates.appendChild(cell);
    }
}

// Modal functions
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.add('show');
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('show');
}

// Initialize tab functionality
function initializeTabs() {
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    const shiftControls = document.getElementById('shiftControls');
    const workRuleControls = document.getElementById('workRuleControls');

    // Set initial state
    const initialTab = document.querySelector('.tab.active');
    if (initialTab) {
        const targetId = initialTab.getAttribute('data-tab');
        const targetContent = document.getElementById(targetId);
        if (targetContent) {
            targetContent.classList.add('active');
        }
        updateControlsVisibility(targetId);
    }

    tabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = tab.getAttribute('data-tab');

            // Update tab states
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');

            // Update content visibility
            tabContents.forEach(content => {
                content.classList.remove('active');
                // Use setTimeout to ensure smooth transition
                if (content.id === targetId) {
                    setTimeout(() => content.classList.add('active'), 50);
                }
            });

            // Update controls visibility
            updateControlsVisibility(targetId);
        });
    });
}

function updateControlsVisibility(activeTabId) {
    const shiftControls = document.getElementById('shiftControls');
    const workRuleControls = document.getElementById('workRuleControls');
    
    if (shiftControls && workRuleControls) {
        shiftControls.style.display = activeTabId === 'shiftSchedule' ? 'flex' : 'none';
        workRuleControls.style.display = activeTabId === 'workRules' ? 'flex' : 'none';
    }
}

// Tab switching functionality
const tabs = document.querySelectorAll('.tab');
const tabContents = document.querySelectorAll('.tab-content');
const shiftControls = document.getElementById('shiftControls');
const workRuleControls = document.getElementById('workRuleControls');

tabs.forEach(tab => {
    tab.addEventListener('click', () => {
        const targetTab = tab.getAttribute('data-tab');
        
        // Remove active class from all tabs and contents
        tabs.forEach(t => t.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));
        
        // Add active class to clicked tab and corresponding content
        tab.classList.add('active');
        document.getElementById(targetTab).classList.add('active');
        
        // Toggle visibility of control buttons based on active tab
        if (targetTab === 'shiftSchedule') {
            shiftControls.style.display = 'flex';
            workRuleControls.style.display = 'none';
        } else if (targetTab === 'workRules') {
            shiftControls.style.display = 'none';
            workRuleControls.style.display = 'flex';
        } else {
            shiftControls.style.display = 'none';
            workRuleControls.style.display = 'none';
        }
    });
});

// Schedule form handling
const scheduleForm = document.querySelector('#addScheduleModal form');
if (scheduleForm) {
    scheduleForm.addEventListener('submit', (e) => {
        e.preventDefault();
        // Handle schedule form submission
        // This would typically send the data to your backend
        hideModal('addScheduleModal');
    });
}

// Work Rules functionality
function editWorkRule(ruleType) {
    const modal = document.getElementById('addWorkRuleModal');
    const ruleFields = document.getElementById('ruleFields');
    const ruleSelect = modal.querySelector('select');
    
    // Set the rule type in the select
    ruleSelect.value = ruleType;
    
    // Generate fields based on rule type
    ruleFields.innerHTML = generateRuleFields(ruleType);
    
    // Load current values
    loadCurrentRuleValues(ruleType);
    
    showModal('addWorkRuleModal');
}

function generateRuleFields(ruleType) {
    switch(ruleType) {
        case 'standard':
            return `
                <div class="form-row">
                    <div class="form-group">
                        <label>Jam Mulai</label>
                        <input type="time" name="startTime" required>
                    </div>
                    <div class="form-group">
                        <label>Jam Selesai</label>
                        <input type="time" name="endTime" required>
                    </div>
                </div>
                <div class="form-group">
                    <label>Toleransi Keterlambatan (menit)</label>
                    <input type="number" name="tolerance" min="0" max="60" required>
                </div>`;
        
        case 'shifts':
            return `
                <div class="shift-form-container">
                    <div class="shift-form" data-shift="morning">
                        <h4>Shift Pagi</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Mulai</label>
                                <input type="time" name="morningStart" required>
                            </div>
                            <div class="form-group">
                                <label>Selesai</label>
                                <input type="time" name="morningEnd" required>
                            </div>
                        </div>
                    </div>
                    <div class="shift-form" data-shift="afternoon">
                        <h4>Shift Siang</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Mulai</label>
                                <input type="time" name="afternoonStart" required>
                            </div>
                            <div class="form-group">
                                <label>Selesai</label>
                                <input type="time" name="afternoonEnd" required>
                            </div>
                        </div>
                    </div>
                    <div class="shift-form" data-shift="night">
                        <h4>Shift Malam</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Mulai</label>
                                <input type="time" name="nightStart" required>
                            </div>
                            <div class="form-group">
                                <label>Selesai</label>
                                <input type="time" name="nightEnd" required>
                            </div>
                        </div>
                    </div>
                </div>`;
        
        case 'workdays':
            return `
                <div class="form-group">
                    <label>Hari Kerja</label>
                    <div class="workdays-select">
                        <label class="workday-checkbox">
                            <input type="checkbox" name="workdays" value="1"> Senin
                        </label>
                        <label class="workday-checkbox">
                            <input type="checkbox" name="workdays" value="2"> Selasa
                        </label>
                        <label class="workday-checkbox">
                            <input type="checkbox" name="workdays" value="3"> Rabu
                        </label>
                        <label class="workday-checkbox">
                            <input type="checkbox" name="workdays" value="4"> Kamis
                        </label>
                        <label class="workday-checkbox">
                            <input type="checkbox" name="workdays" value="5"> Jumat
                        </label>
                        <label class="workday-checkbox">
                            <input type="checkbox" name="workdays" value="6"> Sabtu
                        </label>
                        <label class="workday-checkbox">
                            <input type="checkbox" name="workdays" value="0"> Minggu
                        </label>
                    </div>
                </div>`;
        
        case 'overtime':
            return `
                <div class="form-row">
                    <div class="form-group">
                        <label>Batas Maksimal per Hari (jam)</label>
                        <input type="number" name="maxOvertimeHours" min="1" max="12" required>
                    </div>
                    <div class="form-group">
                        <label>Kompensasi (× gaji per jam)</label>
                        <input type="number" name="overtimeRate" min="1" max="5" step="0.5" required>
                    </div>
                </div>
                <div class="form-group">
                    <label>Persyaratan Khusus</label>
                    <textarea name="overtimeRequirements" rows="3"></textarea>
                </div>`;
        
        case 'break':
            return `
                <div class="form-row">
                    <div class="form-group">
                        <label>Durasi Istirahat (menit)</label>
                        <input type="number" name="breakDuration" min="15" max="120" step="15" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Jam Mulai Istirahat</label>
                        <input type="time" name="breakStart" required>
                    </div>
                    <div class="form-group">
                        <label>Jam Selesai Istirahat</label>
                        <input type="time" name="breakEnd" required>
                    </div>
                </div>`;
        
        default:
            return '<p>Pilih jenis aturan untuk menampilkan form.</p>';
    }
}

function loadCurrentRuleValues(ruleType) {
    const modal = document.getElementById('addWorkRuleModal');
    
    switch(ruleType) {
        case 'standard':
            modal.querySelector('[name="startTime"]').value = "08:00";
            modal.querySelector('[name="endTime"]').value = "17:00";
            modal.querySelector('[name="tolerance"]').value = "15";
            break;
            
        case 'shifts':
            modal.querySelector('[name="morningStart"]').value = "08:00";
            modal.querySelector('[name="morningEnd"]').value = "16:00";
            modal.querySelector('[name="afternoonStart"]').value = "16:00";
            modal.querySelector('[name="afternoonEnd"]').value = "00:00";
            modal.querySelector('[name="nightStart"]').value = "00:00";
            modal.querySelector('[name="nightEnd"]').value = "08:00";
            break;
            
        case 'workdays':
            const workdays = [1, 2, 3, 4, 5]; // Mon-Fri
            const checkboxes = modal.querySelectorAll('[name="workdays"]');
            checkboxes.forEach(cb => {
                cb.checked = workdays.includes(parseInt(cb.value));
            });
            break;
            
        case 'overtime':
            modal.querySelector('[name="maxOvertimeHours"]').value = "4";
            modal.querySelector('[name="overtimeRate"]').value = "1.5";
            break;
            
        case 'break':
            modal.querySelector('[name="breakDuration"]').value = "60";
            modal.querySelector('[name="breakStart"]').value = "12:00";
            modal.querySelector('[name="breakEnd"]').value = "13:00";
            break;
    }
}

// Handle rule type change in modal
const ruleTypeSelect = document.querySelector('#addWorkRuleModal select');
if (ruleTypeSelect) {
    ruleTypeSelect.addEventListener('change', (e) => {
        const ruleFields = document.getElementById('ruleFields');
        ruleFields.innerHTML = generateRuleFields(e.target.value);
        loadCurrentRuleValues(e.target.value);
    });
}

// Handle work rule form submission
const workRuleForm = document.querySelector('#addWorkRuleModal form');
if (workRuleForm) {
    workRuleForm.addEventListener('submit', (e) => {
        e.preventDefault();
        
        // Get form data
        const formData = new FormData(workRuleForm);
        const ruleType = formData.get('ruleType');
        
        // Save changes (this would typically interact with your backend)
        saveWorkRuleChanges(ruleType, formData);
        
        // Update UI
        updateWorkRuleDisplay(ruleType, formData);
        
        // Close modal
        hideModal('addWorkRuleModal');
    });
}

function saveWorkRuleChanges(ruleType, formData) {
    // Here you would typically send the data to your backend
    console.log('Saving work rule changes:', {
        type: ruleType,
        data: Object.fromEntries(formData.entries())
    });
}

function updateWorkRuleDisplay(ruleType, formData) {
    // Update the display of the work rule card based on the type
    const card = document.querySelector(`.rule-card[data-rule="${ruleType}"]`);
    if (!card) return;
    
    switch(ruleType) {
        case 'standard':
            card.querySelector('.time-display:nth-of-type(1)').textContent = formData.get('startTime');
            card.querySelector('.time-display:nth-of-type(2)').textContent = formData.get('endTime');
            card.querySelector('.time-display:nth-of-type(3)').textContent = `${formData.get('tolerance')} menit`;
            break;
        // Add other cases as needed
    }
}

// Holidays functionality
document.addEventListener('DOMContentLoaded', function() {
    // Handle holiday form submission
    const holidayForm = document.querySelector('#holidayForm');
    if (holidayForm) {
        holidayForm.addEventListener('submit', (e) => {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(holidayForm);
            const date = new Date(formData.get('holidayDate'));
            
            // Format date and get day name
            const dayNames = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
            const monthNames = ['Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni', 
                              'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'];
            
            const formattedDate = `${date.getDate()} ${monthNames[date.getMonth()]}`;
            const dayName = dayNames[date.getDay()];
            const holidayName = formData.get('holidayName');
            
            // Add new holiday to table
            addHolidayToTable(formattedDate, dayName, holidayName);
            
            // Clear form and close modal
            holidayForm.reset();
            hideModal('addHolidayModal');
        });
    }
});

function addHolidayToTable(date, day, name) {
    const tbody = document.querySelector('.holidays-table tbody');
    if (!tbody) return;
    
    const tr = document.createElement('tr');
    tr.innerHTML = `
        <td>${date}</td>
        <td>${day}</td>
        <td>${name}</td>
        <td class="actions">
            <button class="btn-icon" title="Edit" onclick="editHoliday(this)">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn-icon" title="Hapus" onclick="deleteHoliday(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    
    tbody.appendChild(tr);
}

function editHoliday(button) {
    const row = button.closest('tr');
    const cells = row.cells;
    
    // Get current values
    const date = cells[0].textContent;
    const name = cells[2].textContent;
    
    // Set values in modal
    const modal = document.getElementById('addHolidayModal');
    modal.querySelector('h2').textContent = 'Edit Hari Libur';
    modal.querySelector('input[name="holidayName"]').value = name;
    
    // Show modal
    showModal('addHolidayModal');
}

function deleteHoliday(button) {
    if (confirm('Apakah Anda yakin ingin menghapus hari libur ini?')) {
        button.closest('tr').remove();
    }
}

function exportHolidays() {
    const table = document.querySelector('.holidays-table');
    const rows = Array.from(table.querySelectorAll('tbody tr'));
    
    let csv = 'Tanggal,Hari,Hari Libur\n';
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        csv += `${cells[0].textContent},${cells[1].textContent},${cells[2].textContent}\n`;
    });
    
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', 'hari_libur_2025.csv');
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}
