<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Man<PERSON><PERSON><PERSON> - Sistem Absensi</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/dashboard.css">
    <link rel="stylesheet" href="../assets/admin-pages.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-user-clock"></i>
                </div>
                <h2>Sistem Absensi</h2>
            </div>
            <nav class="sidebar-nav">
                <a href="admin-dashboard.html">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
                <a href="admin-employees.html" class="active">
                    <i class="fas fa-users"></i>
                    <span>Karyawan</span>
                </a>
                <a href="admin-attendance.html">
                    <i class="fas fa-clock"></i>
                    <span>Kehadiran</span>
                </a>
                <a href="admin-schedule.html">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Jadwal</span>
                </a>
                <a href="admin-reports.html">
                    <i class="fas fa-file-alt"></i>
                    <span>Laporan</span>
                </a>
                <a href="admin-settings.html">
                    <i class="fas fa-cog"></i>
                    <span>Pengaturan</span>
                </a>
                <a href="admin-profile.html">
                    <i class="fas fa-user"></i>
                    <span>Profil</span>
                </a>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.html">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="page-title">
                    <h1>Manajemen Karyawan</h1>
                </div>
                <div class="top-bar-right">
                    <div class="search-bar">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Cari karyawan...">
                    </div>
                    <button class="add-button" onclick="showModal('addEmployeeModal')">
                        <i class="fas fa-plus"></i>
                        Tambah Karyawan
                    </button>
                </div>
            </header>

            <!-- Employee Filters -->
            <div class="filter-section">
                <div class="filter-group">
                    <label>Departemen:</label>
                    <select>
                        <option value="">Semua Departemen</option>
                        <option value="it">IT</option>
                        <option value="hr">HR</option>
                        <option value="finance">Finance</option>
                        <option value="marketing">Marketing</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Status:</label>
                    <select>
                        <option value="">Semua Status</option>
                        <option value="active">Aktif</option>
                        <option value="inactive">Tidak Aktif</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Urutkan:</label>
                    <select>
                        <option value="name">Nama</option>
                        <option value="id">ID Karyawan</option>
                        <option value="department">Departemen</option>
                        <option value="joinDate">Tanggal Bergabung</option>
                    </select>
                </div>
            </div>

            <!-- Employee Table -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll">
                            </th>
                            <th>ID Karyawan</th>
                            <th>Nama</th>
                            <th>Departemen</th>
                            <th>Jabatan</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody id="employeeTableBody">
                        <tr>
                            <td><input type="checkbox"></td>
                            <td>EMP001</td>
                            <td>
                                <div class="employee-info">
                                    <img src="https://ui-avatars.com/api/?name=John+Doe&background=4f46e5&color=fff" alt="John Doe">
                                    <span>John Doe</span>
                                </div>
                            </td>
                            <td>IT</td>
                            <td>Senior Developer</td>
                            <td><EMAIL></td>
                            <td><span class="status-badge active">Aktif</span></td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-btn edit" onclick="showModal('editEmployeeModal')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn view" onclick="showModal('viewEmployeeModal')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn delete" onclick="showModal('deleteEmployeeModal')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <!-- Add more employee rows here -->
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination">
                <button class="page-btn" disabled>
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="page-btn active">1</button>
                <button class="page-btn">2</button>
                <button class="page-btn">3</button>
                <span class="page-separator">...</span>
                <button class="page-btn">10</button>
                <button class="page-btn">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </main>
    </div>

    <!-- Add Employee Modal -->
    <div id="addEmployeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Tambah Karyawan Baru</h2>
                <button class="close-btn" onclick="hideModal('addEmployeeModal')">&times;</button>
            </div>
            <form class="modal-form" id="addEmployeeForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="addFullName">Nama Lengkap</label>
                        <input type="text" id="addFullName" name="fullName" required>
                    </div>
                    <div class="form-group">
                        <label for="addEmail">Email</label>
                        <input type="email" id="addEmail" name="email" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="addDepartment">Departemen</label>
                        <select id="addDepartment" name="department" required>
                            <option value="">Pilih Departemen</option>
                            <option value="it">IT</option>
                            <option value="hr">HR</option>
                            <option value="finance">Finance</option>
                            <option value="marketing">Marketing</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="addPosition">Jabatan</label>
                        <input type="text" id="addPosition" name="position" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="addJoinDate">Tanggal Bergabung</label>
                        <input type="date" id="addJoinDate" name="joinDate" required>
                    </div>
                    <div class="form-group">
                        <label for="addPhone">Nomor Telepon</label>
                        <input type="tel" id="addPhone" name="phone" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="addAddress">Alamat</label>
                    <textarea id="addAddress" name="address" rows="3"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn secondary" onclick="hideModal('addEmployeeModal')">Batal</button>
                    <button type="submit" class="btn primary">Simpan</button>
                </div>
            </form>
        </div>
    </div>

    <script defer src="../assets/dashboard.js?v=1"></script>
    <script defer src="../assets/admin-pages.js?v=1"></script>
</body>
</html>
