// Settings Form Handler
document.addEventListener('DOMContentLoaded', function() {
    const settingsForm = document.getElementById('settingsForm');
    
    if (settingsForm) {
        settingsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Here we would normally collect all form data and send it to the server
            const formData = new FormData(this);
            console.log('Saving settings...');
            
            // Simulate saving with a loading state
            const saveButton = document.querySelector('.save-all');
            const originalText = saveButton.innerHTML;
            saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Menyimpan...';
            saveButton.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                saveButton.innerHTML = '<i class="fas fa-check"></i> Tersimpan!';
                
                // Reset button after 2 seconds
                setTimeout(() => {
                    saveButton.innerHTML = originalText;
                    saveButton.disabled = false;
                }, 2000);
                
                // Show success message (you can replace this with your preferred notification system)
                alert('Pengaturan berhasil disimpan!');
            }, 1500);
        });
    }
});

// Database Backup Handler
function backupDatabase() {
    const backupButton = document.querySelector('.backup-button');
    const originalText = backupButton.innerHTML;
    
    // Show loading state
    backupButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';
    backupButton.disabled = true;
    
    // Simulate backup process
    setTimeout(() => {
        // Success state
        backupButton.innerHTML = '<i class="fas fa-check"></i> Backup Selesai!';
        
        // Reset button after 2 seconds
        setTimeout(() => {
            backupButton.innerHTML = originalText;
            backupButton.disabled = false;
        }, 2000);
        
        // Simulate downloading a backup file
        const date = new Date().toISOString().split('T')[0];
        const dummyLink = document.createElement('a');
        dummyLink.href = '#';
        dummyLink.download = `backup_${date}.sql`;
        dummyLink.click();
        
        // Show success message
        alert('Backup database berhasil!');
    }, 2000);
}
