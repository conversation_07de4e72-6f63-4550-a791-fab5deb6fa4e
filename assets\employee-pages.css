/* Additional styles for employee pages */

/* Table Styles */
.attendance-table-container, .request-table-container {
    overflow-x: auto;
    background: white;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.attendance-table, .request-table {
    width: 100%;
    border-collapse: collapse;
}

.attendance-table th, .request-table th {
    background-color: var(--background);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 1px solid var(--border-color);
}

.attendance-table td, .request-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.attendance-table tr:last-child td, .request-table tr:last-child td {
    border-bottom: none;
}

.attendance-table tr:hover, .request-table tr:hover {
    background-color: var(--background);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.pagination-btn {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.pagination-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Calendar Styles */
.calendar-container {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.calendar-header h3 {
    font-size: 1.25rem;
}

.calendar-nav-btn {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendar-nav-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.calendar-weekdays div {
    padding: 0.5rem;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.calendar-day:hover:not(.empty) {
    background: #e5e7eb;
}

.calendar-day.empty {
    background: transparent;
    cursor: default;
}

.calendar-day.today {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

.calendar-day.off {
    background: #fee2e2;
    color: var(--danger-color);
}

.calendar-legend {
    display: flex;
    gap: 1.5rem;
    margin-top: 1rem;
    justify-content: center;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 4px;
}

/* Shift Cards */
.shift-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.shift-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.shift-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.shift-header i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.shift-header h4 {
    font-size: 1.1rem;
    margin: 0;
}

.shift-time {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.shift-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.shift-details p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-light);
}

/* Filter Controls */
.filter-controls {
    display: flex;
    gap: 1rem;
}

.filter-controls select {
    padding: 0.5rem 2rem 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    color: var(--text-color);
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.7rem center;
    background-size: 1em;
}

.filter-controls select:hover {
    border-color: var(--primary-color);
}

/* Request Form */
.request-form-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.request-form {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 600px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 90vh;
    overflow-y: auto;
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--text-light);
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: var(--danger-color);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: border 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1rem;
}

.cancel-btn {
    padding: 0.75rem 1.5rem;
    background: var(--background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cancel-btn:hover {
    background: #e5e7eb;
}

.submit-btn {
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    background: var(--primary-hover);
}

/* Request Filters */
.request-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
    background: white;
    padding: 1rem;
    border-radius: 8px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
}

.filter-group select {
    padding: 0.5rem 2rem 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--background);
    font-size: 0.9rem;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 0.8em;
}

/* Request Status */
.request-status {
    padding: 0.375rem 0.75rem;
    border-radius: 999px;
    font-size: 0.875rem;
    display: inline-block;
}

.request-status.approved {
    background: #dcfce7;
    color: var(--success-color);
}

.request-status.rejected {
    background: #fee2e2;
    color: var(--danger-color);
}

.request-status.pending {
    background: #fef3c7;
    color: var(--warning-color);
}

/* Action Icons */
.action-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: var(--background);
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 0.25rem;
}

.action-icon:hover {
    background: #e5e7eb;
}

.view-btn:hover {
    color: var(--primary-color);
}

.edit-btn:hover {
    color: var(--warning-color);
}

.delete-btn:hover {
    color: var(--danger-color);
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 700px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

/* Request Detail */
.request-detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.detail-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.detail-group.full-width {
    grid-column: 1 / -1;
}

.detail-group label {
    font-weight: 600;
    color: var(--text-light);
}

.attachment-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
}

.attachment-link:hover {
    text-decoration: underline;
}

/* Timeline */
.request-timeline {
    margin-top: 2rem;
}

.request-timeline h4 {
    margin-bottom: 1rem;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 7px;
    top: 0;
    height: 100%;
    width: 2px;
    background: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-icon {
    position: absolute;
    left: -2rem;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: white;
    border: 2px solid var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 0.5rem;
}

.timeline-content h5 {
    margin: 0 0 0.25rem 0;
}

.timeline-content p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .request-filters {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-group select {
        width: 100%;
    }
    
    .request-detail-grid {
        grid-template-columns: 1fr;
    }
}