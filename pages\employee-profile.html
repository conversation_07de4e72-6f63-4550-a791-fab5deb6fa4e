<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil Saya - Sistem Absensi</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/dashboard.css">
    <link rel="stylesheet" href="../assets/employee-pages.css">
    <link rel="stylesheet" href="../assets/profile.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-user-clock"></i>
                </div>
                <h2>Sistem Absensi</h2>
            </div>
            <nav class="sidebar-nav">
                <a href="employee-dashboard.html">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
                <a href="employee-schedule.html">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Jadwal</span>
                </a>
                <a href="employee-reports.html">
                    <i class="fas fa-file-alt"></i>
                    <span>Laporan Saya</span>
                </a>
                <a href="employee-requests.html">
                    <i class="fas fa-envelope"></i>
                    <span>Pengajuan</span>
                </a>
                <a href="employee-profile.html" class="active">
                    <i class="fas fa-user"></i>
                    <span>Profil</span>
                </a>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.html">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="date-time">
                    <h2 id="currentTime">08:00:00</h2>
                    <p id="currentDate">Senin, 19 Juni 2025</p>
                </div>
                <div class="top-bar-right">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">2</span>
                    </div>
                    <div class="admin-profile" id="userProfile">
                        <img src="https://ui-avatars.com/api/?name=John+Doe&background=4f46e5&color=fff" alt="John Doe">
                        <span>John Doe</span>
                    </div>
                </div>
            </header>

            <!-- Profile Content -->
            <div class="profile-content">
                <!-- Profile Header -->
                <div class="profile-header-card">
                    <div class="profile-cover">
                        <div class="profile-avatar-large">
                            <img src="https://ui-avatars.com/api/?name=John+Doe&background=4f46e5&color=fff&size=120" alt="John Doe">
                            <button class="avatar-edit-btn" id="changeAvatarBtn">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                    </div>
                    <div class="profile-info">
                        <h2 id="userName">John Doe</h2>
                        <p class="employee-id" id="userId">EMP001</p>
                        <p class="position" id="userPosition">Software Developer</p>
                        <div class="profile-stats" id="profileStats">
                            <div class="stat-item">
                                <span class="stat-number">22</span>
                                <span class="stat-label">Hari Hadir</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">164</span>
                                <span class="stat-label">Jam Kerja</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">10</span>
                                <span class="stat-label">Sisa Cuti</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">95%</span>
                                <span class="stat-label">Kehadiran</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Sections -->
                <div class="profile-sections">
                    <!-- Personal Information -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3><i class="fas fa-user"></i> Informasi Pribadi</h3>
                            <button class="edit-btn" id="editPersonalBtn">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                        </div>
                        <div class="section-content">
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Nama Lengkap</label>
                                    <p id="fullName">John Doe</p>
                                </div>
                                <div class="info-item">
                                    <label>Email</label>
                                    <p id="userEmail"><EMAIL></p>
                                </div>
                                <div class="info-item">
                                    <label>Nomor Telepon</label>
                                    <p id="userPhone">+62 812-3456-7890</p>
                                </div>
                                <div class="info-item">
                                    <label>Alamat</label>
                                    <p id="userAddress">Jl. Merdeka No. 45, Jakarta Selatan</p>
                                </div>
                                <div class="info-item">
                                    <label>Tanggal Lahir</label>
                                    <p id="userBirthDate">25 Agustus 1990</p>
                                </div>
                                <div class="info-item">
                                    <label>Jenis Kelamin</label>
                                    <p id="userGender">Laki-laki</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Employment Information -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3><i class="fas fa-briefcase"></i> Informasi Kepegawaian</h3>
                        </div>
                        <div class="section-content">
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>ID Karyawan</label>
                                    <p id="employeeId">EMP001</p>
                                </div>
                                <div class="info-item">
                                    <label>Jabatan</label>
                                    <p id="jobTitle">Software Developer</p>
                                </div>
                                <div class="info-item">
                                    <label>Departemen</label>
                                    <p id="department">IT Development</p>
                                </div>
                                <div class="info-item">
                                    <label>Tanggal Bergabung</label>
                                    <p id="joinDate">15 Maret 2022</p>
                                </div>
                                <div class="info-item">
                                    <label>Status</label>
                                    <span class="status-badge active" id="employmentStatus">Aktif</span>
                                </div>
                                <div class="info-item">
                                    <label>Atasan Langsung</label>
                                    <p id="supervisor">Sarah Johnson (IT Manager)</p>
                                </div>
                                <div class="info-item">
                                    <label>Jam Kerja</label>
                                    <p id="workingHours">08:00 - 17:00 WIB</p>
                                </div>
                                <div class="info-item">
                                    <label>Lokasi Kerja</label>
                                    <p id="workLocation">Jakarta Office</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Employee Performance Section -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3><i class="fas fa-chart-line"></i> Performa Kehadiran</h3>
                        </div>
                        <div class="section-content">
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Tingkat Kehadiran</label>
                                    <p style="color: #16a34a; font-weight: 600;">95% (Sangat Baik)</p>
                                </div>
                                <div class="info-item">
                                    <label>Total Hari Kerja</label>
                                    <p>23 hari (Bulan ini)</p>
                                </div>
                                <div class="info-item">
                                    <label>Keterlambatan</label>
                                    <p>2 kali (Bulan ini)</p>
                                </div>
                                <div class="info-item">
                                    <label>Lembur</label>
                                    <p>8 jam (Bulan ini)</p>
                                </div>
                                <div class="info-item">
                                    <label>Cuti Digunakan</label>
                                    <p>2 hari (Tahun ini)</p>
                                </div>
                                <div class="info-item">
                                    <label>Sisa Cuti</label>
                                    <p style="color: #4f46e5; font-weight: 600;">10 hari</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3><i class="fas fa-bolt"></i> Aksi Cepat</h3>
                        </div>
                        <div class="section-content">
                            <div class="quick-actions-grid">
                                <button class="quick-action-btn" onclick="window.location.href='employee-requests.html'">
                                    <i class="fas fa-calendar-plus"></i>
                                    <span>Ajukan Cuti</span>
                                </button>
                                <button class="quick-action-btn" onclick="window.location.href='employee-requests.html'">
                                    <i class="fas fa-clock"></i>
                                    <span>Izin Terlambat</span>
                                </button>
                                <button class="quick-action-btn" onclick="window.location.href='employee-reports.html'">
                                    <i class="fas fa-download"></i>
                                    <span>Unduh Laporan</span>
                                </button>
                                <button class="quick-action-btn" onclick="window.location.href='employee-schedule.html'">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Lihat Jadwal</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Account Settings -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3><i class="fas fa-cog"></i> Pengaturan Akun</h3>
                        </div>
                        <div class="section-content">
                            <div class="settings-list">
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Ubah Password</h4>
                                        <p>Perbarui password akun Anda</p>
                                    </div>
                                    <button class="setting-btn" id="changePasswordBtn">
                                        <i class="fas fa-key"></i> Ubah
                                    </button>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Notifikasi Email</h4>
                                        <p>Kelola pengaturan notifikasi email</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Notifikasi Push</h4>
                                        <p>Terima notifikasi real-time</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Mode Gelap</h4>
                                        <p>Aktifkan tema gelap</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Bahasa</h4>
                                        <p>Pilih bahasa tampilan aplikasi</p>
                                    </div>
                                    <select style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 6px;">
                                        <option value="id">Bahasa Indonesia</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Zona Waktu</h4>
                                        <p>Atur zona waktu untuk absensi</p>
                                    </div>
                                    <select style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 6px;">
                                        <option value="wib">WIB (UTC+7)</option>
                                        <option value="wita">WITA (UTC+8)</option>
                                        <option value="wit">WIT (UTC+9)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Change Password Modal -->
    <div class="modal" id="passwordModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Ubah Password</h3>
                <button class="close-modal" id="closePasswordModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="password-form" id="passwordForm">
                <div class="form-group">
                    <label for="currentPassword">Password Saat Ini</label>
                    <input type="password" id="currentPassword" required>
                </div>
                <div class="form-group">
                    <label for="newPassword">Password Baru</label>
                    <input type="password" id="newPassword" required>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Konfirmasi Password Baru</label>
                    <input type="password" id="confirmPassword" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" id="cancelPassword">Batal</button>
                    <button type="submit" class="btn-primary">Simpan Password</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/dashboard.js"></script>
    <script src="../assets/employee-profile.js"></script>
</body>
</html>
