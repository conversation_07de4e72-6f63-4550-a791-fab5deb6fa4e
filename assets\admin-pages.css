/* Variables */
:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --text-color: #1f2937;
    --text-muted: #6b7280;
    --border-color: #e5e7eb;
    --background-light: #f9fafb;
    --hover-color: #f3f4f6;
    --error-color: #dc2626;
    --success-color: #16a34a;
    --warning-color: #ca8a04;
}

/* Admin Pages Specific Styles */

/* Page Title */
.page-title {
    margin: 0;
}

.page-title h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    padding-left: 12px;
}

/* Top Bar Styles */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: 12px;   
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* <PERSON><PERSON> Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.btn i {
    font-size: 1rem;
}

.btn.primary {
    background: var(--primary-color);
    color: white;
}

.btn.primary:hover {
    background: var(--primary-color);
    opacity: 0.9;
}

.btn.secondary {
    background: var(--background-light);
    color: var(--text-color);
    border-color: var(--border-color);
}

.btn.secondary:hover {
    background: var(--hover-color);
}

.btn.secondary-outline {
    background: transparent;
    color: var(--text-color);
    border-color: var(--border-color);
}

.btn.secondary-outline:hover {
    background: var(--secondary-color);
    color: white;
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 6px;
    background: var(--background-light);
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-icon:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.btn-icon i {
    font-size: 14px;
}

/* Form Styles */
.form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.875rem;
    color: var(--text-color);
    background: #fff;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Add Button */
.add-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.add-button i {
    font-size: 0.9em;
}

.add-button:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

/* Filter Section */
.filter-section {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.filter-group select {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.875rem;
    color: var(--text-color);
    background: #fff;
}

.filter-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: #fff;
    color: var(--text-color);
    font-size: 0.875rem;
    cursor: pointer;
}

.filter-button:hover {
    background: var(--hover-color);
}

/* Table Styles */
.table-container {
    background: white;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: var(--background-light);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
}

.employee-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.employee-info img {
    width: 32px;
    height: 32px;
    border-radius: 999px;
}

.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 999px;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-block;
}

.status-badge.active {
    background: #dcfce7;
    color: var(--success-color);
}

.status-badge.inactive {
    background: #fee2e2;
    color: var(--danger-color);
}

.status-badge.present {
    background: #dcfce7;
    color: var(--success-color);
}

.status-badge.late {
    background: #fef3c7;
    color: var(--warning-color);
}

.status-badge.absent {
    background: #fee2e2;
    color: var(--danger-color);
}

.status-badge.leave {
    background: #e0e7ff;
    color: var(--primary-color);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn.edit {
    background: #e0e7ff;
    color: var(--primary-color);
}

.action-btn.view {
    background: #dcfce7;
    color: var(--success-color);
}

.action-btn.delete {
    background: #fee2e2;
    color: var(--danger-color);
}

.action-btn:hover {
    transform: translateY(-2px);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    padding: 20px;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 12px;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.modal-form {
    padding: 24px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.close-btn:hover {
    color: var(--text-color);
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.summary-card {
    background: white;
    padding: 1.25rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.summary-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.summary-details h3 {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

.summary-details p {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.page-btn {
    min-width: 36px;
    height: 36px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-color);
}

.page-btn:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.page-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.page-btn:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.page-separator {
    color: var(--text-light);
}

/* Schedule Page Styles */
.tabs-container {
    margin-bottom: 20px;
}

.tabs {
    display: flex;
    gap: 10px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1px;
}

.tab {
    padding: 10px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 1em;
    color: var(--text-muted);
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab:hover {
    color: var(--primary-color);
}

.tab.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.calendar-container {
    background: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.calendar-navigation {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.calendar-navigation h2 {
    font-size: 1.25rem;
    margin: 0;
}

.nav-btn {
    background: var(--background-light);
    border: none;
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nav-btn:hover {
    background: var(--border-color);
}

.calendar-grid {
    display: grid;
    gap: 1px;
    background: var(--border-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: var(--background-light);
}

.calendar-days div {
    padding: 10px;
    text-align: center;
    font-weight: 600;
    background: #ffffff;
}

.calendar-dates {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.calendar-cell {
    background: #ffffff;
    min-height: 100px;
    padding: 10px;
    position: relative;
}

.calendar-cell.empty {
    background: var(--background-light);
}

.date-number {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.calendar-cell.current .date-number {
    background: var(--primary-color);
    color: #ffffff;
}

.shift-indicators {
    margin-top: 25px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.shift-indicator {
    width: 100%;
    height: 4px;
    border-radius: 2px;
}

.shift-indicator.morning {
    background-color: #1976d2;
}

.shift-indicator.afternoon {
    background-color: #f57c00;
}

.shift-indicator.night {
    background-color: #3f51b5;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    padding: 20px;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: #fff;
    border-radius: 12px;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.modal-form {
    padding: 24px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.close-btn:hover {
    color: var(--text-color);
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.summary-card {
    background: white;
    padding: 1.25rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.summary-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.summary-details h3 {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

.summary-details p {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

/* Pagination */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.page-btn {
    min-width: 36px;
    height: 36px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-color);
}

.page-btn:hover:not(:disabled) {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.page-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.page-btn:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.page-separator {
    color: var(--text-light);
}

/* Shift Summary Styles */
.shift-summary {
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    margin-top: 24px;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.summary-header h3 {
    font-size: 1.25rem;
    color: var(--text-color);
    margin: 0;
    font-weight: 600;
}

.summary-date {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-muted);
    font-size: 0.95rem;
}

.summary-date i {
    color: var(--primary-color);
}

.shift-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.shift-card {
    background: var(--background-light);
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid var(--border-color);
}

.shift-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.shift-card-header {
    padding: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    background: #fff;
    border-bottom: 1px solid var(--border-color);
}

.shift-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 6px 16px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.95rem;
    min-width: 120px;
    width: 120px;
    text-align: center;
}

.shift-badge i {
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.shift-badge.morning {
    background-color: #e3f2fd;
    color: #1976d2;
}

.shift-badge.afternoon {
    background-color: #fff3e0;
    color: #f57c00;
}

.shift-badge.night {
    background-color: #e8eaf6;
    color: #3f51b5;
}

.shift-time {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    color: var(--text-muted);
    font-size: 1rem;
    width: 100%;
    text-align: center;
}

.shift-card-body {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.employee-count {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.employee-count .count {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.employee-count .label {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.shift-actions {
    display: flex;
    gap: 8px;
}

.shift-actions .btn-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    color: var(--text-muted);
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
    background: #fff;
}

.shift-actions .btn-icon:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Work Rules Styles */
.work-rules-container {
    padding: 0px;
}

.rules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.rule-card {
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    min-height: 280px;
}

.rule-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rule-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 12px;
}

.rule-header i {
    font-size: 1.5em;
    color: var(--primary-color);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-light);
    border-radius: 8px;
}

.rule-header h3 {
    margin: 0;
    font-size: 1.2em;
    color: var(--text-color);
    font-weight: 600;
}

.rule-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
}

.rule-item {
    margin-bottom: 12px;
}

.rule-item:last-child {
    margin-bottom: 0;
}

.rule-item label {
    display: block;
    font-size: 0.9em;
    color: var(--text-muted);
    margin-bottom: 4px;
}

.time-display {
    font-size: 1.1em;
    color: var(--text-color);
    font-weight: 500;
}

.shift-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.shift-rule {
    display: flex;
    align-items: center;    
    gap: 12px;
    padding: 8px;
    background: var(--background-light);
    border-radius: 8px;
}

.workdays-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    max-width: 100%;
    overflow: hidden;
}

.workday {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    font-size: 0.9em;
    background: var(--background);
    color: var(--text-muted);
    font-weight: 500;
    transition: all 0.2s ease;
}

.workday.active {
    background: var(--primary-color);
    color: white;
}

.rule-actions {
    display: flex;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

/* Work Rule Modal Fields */
.workdays-select {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-top: 10px;
}

.workday-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.workday-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
}

.shift-form-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.shift-form {
    background: var(--background-light);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.shift-form h4 {
    margin: 0 0 12px 0;
    font-size: 1.1em;
    color: var(--text-color);
}

/* Holidays Styles */
.holidays-container {
    padding: 0;
}

.holidays-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.holidays-header h3 {
    margin: 0;
    font-size: 1.2em;
    color: var(--text-color);
    font-weight: 600;
}

.holidays-actions {
    display: flex;
    gap: 12px;
}

.holidays-table-wrapper {
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.holidays-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 800px;
}

.holidays-table th,
.holidays-table td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.holidays-table th {
    background: var(--background-light);
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
}

.holidays-table td {
    color: var(--text-color);
    vertical-align: middle;
}

.holidays-table tr:hover {
    background-color: var(--hover-color);
}

.holidays-table td:first-child,
.holidays-table th:first-child {
    padding-left: 24px;
}

.holidays-table td:last-child,
.holidays-table th:last-child {
    padding-right: 24px;
}

.holidays-table td.actions {
    width: 100px;
    text-align: right;
    white-space: nowrap;
}

.holidays-table .btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    background: #fff;
    color: var(--text-muted);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    margin-left: 8px;
}

.holidays-table .btn-icon:hover {
    background: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
}

/* Date Input Styling */
.date-picker {
    display: flex;
    align-items: center;
    margin-right: 12px;
}

.date-picker input[type="date"],
.date-picker input[type="month"] {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.875rem;
    color: var(--text-color);
    background: #fff;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    min-width: 150px;
    height: 38px;
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
}

.date-picker input[type="date"]:hover,
.date-picker input[type="month"]:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.date-picker input[type="date"]:focus,
.date-picker input[type="month"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.date-picker input[type="date"]::-webkit-calendar-picker-indicator,
.date-picker input[type="month"]::-webkit-calendar-picker-indicator {
    background-color: transparent;
    padding: 5px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    filter: invert(0.5);
}

.date-picker input[type="date"]::-webkit-calendar-picker-indicator:hover,
.date-picker input[type="month"]::-webkit-calendar-picker-indicator:hover {
    background-color: var(--hover-color);
    filter: invert(0.3);
}

.date-picker input[type="date"]::-webkit-datetime-edit,
.date-picker input[type="month"]::-webkit-datetime-edit {
    color: var(--text-color);
}

.date-picker input[type="date"]::-webkit-datetime-edit-fields-wrapper,
.date-picker input[type="month"]::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
}

.date-picker input[type="date"]::-webkit-datetime-edit-text,
.date-picker input[type="month"]::-webkit-datetime-edit-text {
    color: var(--text-light);
    padding: 0 2px;
}

.date-picker input[type="date"]::-webkit-datetime-edit-month-field,
.date-picker input[type="date"]::-webkit-datetime-edit-day-field,
.date-picker input[type="date"]::-webkit-datetime-edit-year-field,
.date-picker input[type="month"]::-webkit-datetime-edit-month-field,
.date-picker input[type="month"]::-webkit-datetime-edit-year-field {
    color: var(--text-color);
}

/* Date Picker Styles */
.date-picker-wrapper {
    position: relative;
    width: 100%;
    max-width: 300px;
    display: inline-block;
}

.date-picker-input {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    background: #fff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.2s ease;
    padding: 0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.date-picker-input:hover {
    border-color: var(--primary-color);
}

.date-picker-input .fa-calendar {
    color: var(--primary-color);
    font-size: 0.9rem;
    margin-left: 12px;
}

.date-picker-input .chevron-icon {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-right: 12px;
    transition: transform 0.2s ease;
}

.date-picker-input:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.date-picker-input.active .chevron-icon {
    transform: rotate(180deg);
}

.date-picker-input:disabled {
    background-color: var(--background-light);
    cursor: not-allowed;
    opacity: 0.7;
}

.shift-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.schedule-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.schedule-controls-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    width: auto;
}

.schedule-controls-group .btn.primary.add-schedule-btn {
    white-space: nowrap;
    padding: 10px 20px;
    height: 42px;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .workdays-group {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }
    
    .backup-controls .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .backup-button-group {
        width: 100%;
    }
    
    .backup-button-group .invisible {
        display: none;
    }
    
    .btn.primary-outline {
        width: 100%;
        justify-content: center;
    }
    
    .shift-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .schedule-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .rules-grid {
        grid-template-columns: 1fr;
    }
    
    .holidays-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .holidays-actions {
        justify-content: space-between;
    }
    
    .holidays-actions .btn {
        flex: 1;
    }
    
    .holidays-table-wrapper {
        margin: 0 -20px;
        border-radius: 0;
    }
    
    .shift-summary {
        padding: 16px;
    }
    
    .summary-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .shift-cards {
        grid-template-columns: 1fr;
    }
    
    .shift-card-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }
}

/* Notification Styles */
.notification-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.notification-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: var(--background-light);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.notification-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.notification-content h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
}

.notification-content p {
    margin: 0;
    font-size: 14px;
    color: var(--text-muted);
}

/* Switch Toggle Styles */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-color);
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* Workdays Settings Styles */
.workdays-settings {
    margin-top: 24px;
}

.workdays-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 12px;
}

.workdays-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
}

/* Backup Section Styles */
.backup-section {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);
}

.backup-header {
    margin-bottom: 16px;
}

.backup-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
}

.backup-header p {
    margin: 4px 0 0 0;
    font-size: 14px;
    color: var(--text-muted);
}

.backup-controls {
    margin-top: 16px;
}

.backup-select {
    width: 100%;
}

.backup-button {
    height: 38px;
    white-space: nowrap;
}

.invisible {
    visibility: hidden;
}

/* Reports Grid Styles */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    
}

.report-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.report-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.report-header i {
    font-size: 20px;
    color: var(--primary-color);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-light);
    border-radius: 8px;
}

.report-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.report-content {
    margin-bottom: 20px;
}

.report-actions {
    margin-top: auto;
    display: flex;
    justify-content: flex-end;
}

.report-info {

    display: flex;
    flex-direction: column;
    gap: 8px;
}

.report-info p {
    margin: 0;
    font-size: 14px;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: 8px;
}

.report-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: var(--background-light);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
}

/* Setting Page style */
.settings-section {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
}

.settings-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    gap: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.settings-card h2 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.settings-card .form-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
}

.settings-card .form-group:last-child {
    margin-bottom: 0;
}

.settings-card label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
}

.settings-card .actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .settings-section {
        padding: 16px;
    }
    
    .settings-card {
        padding: 16px;
    }
}

.settings-header {
    display: flex;
    align-items: center;
    gap: 12px;
}

.settings-header i {
    font-size: 20px;
    color: var(--primary-color);
    width: 24px;
    text-align: center;
}

.settings-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

@media (max-width: 768px) {
.settings-header {
        margin-bottom: 16px;
    }
}

.bottom-actions {
    height: 3rem;
    width: 100%;
    display: flex;
    justify-content: flex-end;
}

/* Notification Dropdown Styles */
.notification-dropdown {
    position: absolute;
    top: 40px;
    right: 0;
    width: 320px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    z-index: 1001;
    padding: 0;
    border: 1px solid #e5e7eb;
    font-size: 15px;
    animation: fadeInDropdown 0.2s;
}

@keyframes fadeInDropdown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.notification-dropdown .dropdown-header {
    padding: 14px 18px 10px 18px;
    font-weight: 600;
    border-bottom: 1px solid #f3f4f6;
    background: #f9fafb;
}

.notification-dropdown .dropdown-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.notification-dropdown .dropdown-list li {
    padding: 12px 18px;
    border-bottom: 1px solid #f3f4f6;
    color: #374151;
    background: #fff;
}

.notification-dropdown .dropdown-list li:last-child {
    border-bottom: none;
}

.notification-dropdown .dropdown-footer {
    padding: 10px 18px;
    text-align: right;
    background: #f9fafb;
    border-top: 1px solid #f3f4f6;
}

.notification-dropdown .dropdown-footer a {
    color: #4f46e5;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
}

/* Ensure notification icon is relative for dropdown positioning */
.notifications {
    position: relative;
    cursor: pointer;
}
