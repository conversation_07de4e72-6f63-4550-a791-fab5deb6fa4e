<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil Administrator - Sistem Absensi</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/dashboard.css">
    <link rel="stylesheet" href="../assets/admin-pages.css">
    <link rel="stylesheet" href="../assets/profile.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-user-clock"></i>
                </div>
                <h2>Sistem Absensi</h2>
            </div>
            <nav class="sidebar-nav">
                <a href="admin-dashboard.html">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
                <a href="admin-employees.html">
                    <i class="fas fa-users"></i>
                    <span>Karyawan</span>
                </a>
                <a href="admin-attendance.html">
                    <i class="fas fa-clock"></i>
                    <span>Kehadiran</span>
                </a>
                <a href="admin-schedule.html">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Jadwal</span>
                </a>
                <a href="admin-reports.html">
                    <i class="fas fa-file-alt"></i>
                    <span>Laporan</span>
                </a>
                <a href="admin-settings.html">
                    <i class="fas fa-cog"></i>
                    <span>Pengaturan</span>
                </a>
                <a href="admin-profile.html" class="active">
                    <i class="fas fa-user"></i>
                    <span>Profil</span>
                </a>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.html">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="page-title">
                    <h1>Profil Administrator</h1>
                </div>
                <div class="top-bar-right">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">5</span>
                    </div>
                    <div class="admin-profile" id="userProfile">
                        <img src="https://ui-avatars.com/api/?name=Admin&background=4338ca&color=fff" alt="Admin">
                        <span>Administrator</span>
                    </div>
                </div>
            </header>

            <!-- Profile Content -->
            <div class="profile-content">
                <!-- Profile Header -->
                <div class="profile-header-card">
                    <div class="profile-cover admin-cover">
                        <div class="profile-avatar-large">
                            <img src="https://ui-avatars.com/api/?name=Admin&background=4338ca&color=fff&size=120" alt="Admin">
                            <button class="avatar-edit-btn" id="changeAvatarBtn">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                    </div>
                    <div class="profile-info">
                        <h2 id="userName">Administrator</h2>
                        <p class="employee-id" id="userId">ADM001</p>
                        <p class="position" id="userPosition">System Administrator</p>
                        <div class="profile-stats" id="profileStats">
                            <div class="stat-item">
                                <span class="stat-number">150</span>
                                <span class="stat-label">Total Karyawan</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">25</span>
                                <span class="stat-label">Departemen</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">12</span>
                                <span class="stat-label">Pending Requests</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">98%</span>
                                <span class="stat-label">System Uptime</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Sections -->
                <div class="profile-sections">
                    <!-- Personal Information -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3><i class="fas fa-user"></i> Informasi Pribadi</h3>
                            <button class="edit-btn" id="editPersonalBtn">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                        </div>
                        <div class="section-content">
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Nama Lengkap</label>
                                    <p id="fullName">Administrator System</p>
                                </div>
                                <div class="info-item">
                                    <label>Email</label>
                                    <p id="userEmail"><EMAIL></p>
                                </div>
                                <div class="info-item">
                                    <label>Nomor Telepon</label>
                                    <p id="userPhone">+62 811-1234-5678</p>
                                </div>
                                <div class="info-item">
                                    <label>Alamat</label>
                                    <p id="userAddress">Jl. Sudirman No. 123, Jakarta Pusat</p>
                                </div>
                                <div class="info-item">
                                    <label>Tanggal Lahir</label>
                                    <p id="userBirthDate">15 Januari 1980</p>
                                </div>
                                <div class="info-item">
                                    <label>Jenis Kelamin</label>
                                    <p id="userGender">Laki-laki</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Administrative Information -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3><i class="fas fa-shield-alt"></i> Informasi Administrator</h3>
                        </div>
                        <div class="section-content">
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>ID Administrator</label>
                                    <p id="adminId">ADM001</p>
                                </div>
                                <div class="info-item">
                                    <label>Level Akses</label>
                                    <p id="accessLevel" style="color: var(--primary-color); font-weight: 600;">Super Administrator</p>
                                </div>
                                <div class="info-item">
                                    <label>Departemen</label>
                                    <p id="department">IT Management</p>
                                </div>
                                <div class="info-item">
                                    <label>Tanggal Bergabung</label>
                                    <p id="joinDate">1 Januari 2020</p>
                                </div>
                                <div class="info-item">
                                    <label>Status</label>
                                    <span class="status-badge active" id="adminStatus">Aktif</span>
                                </div>
                                <div class="info-item">
                                    <label>Hak Akses</label>
                                    <p id="permissions">Full System Access, User Management, Reports</p>
                                </div>
                                <div class="info-item">
                                    <label>Terakhir Login</label>
                                    <p id="lastLogin">Hari ini, 07:30 WIB</p>
                                </div>
                                <div class="info-item">
                                    <label>IP Address</label>
                                    <p id="ipAddress">*************</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- System Management -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3><i class="fas fa-cogs"></i> Manajemen Sistem</h3>
                        </div>
                        <div class="section-content">
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>Total Karyawan Dikelola</label>
                                    <p style="color: #16a34a; font-weight: 600;">150 Karyawan</p>
                                </div>
                                <div class="info-item">
                                    <label>Departemen Aktif</label>
                                    <p>25 Departemen</p>
                                </div>
                                <div class="info-item">
                                    <label>Pending Approvals</label>
                                    <p style="color: #ca8a04; font-weight: 600;">12 Pengajuan</p>
                                </div>
                                <div class="info-item">
                                    <label>System Uptime</label>
                                    <p style="color: #16a34a; font-weight: 600;">98.5%</p>
                                </div>
                                <div class="info-item">
                                    <label>Database Size</label>
                                    <p>2.4 GB</p>
                                </div>
                                <div class="info-item">
                                    <label>Backup Terakhir</label>
                                    <p>Kemarin, 23:00 WIB</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Admin Quick Actions -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3><i class="fas fa-bolt"></i> Aksi Cepat Administrator</h3>
                        </div>
                        <div class="section-content">
                            <div class="quick-actions-grid">
                                <button class="quick-action-btn" onclick="window.location.href='admin-employees.html'">
                                    <i class="fas fa-users"></i>
                                    <span>Kelola Karyawan</span>
                                </button>
                                <button class="quick-action-btn" onclick="window.location.href='admin-reports.html'">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>Lihat Laporan</span>
                                </button>
                                <button class="quick-action-btn" onclick="window.location.href='admin-settings.html'">
                                    <i class="fas fa-cog"></i>
                                    <span>Pengaturan Sistem</span>
                                </button>
                                <button class="quick-action-btn" onclick="window.location.href='admin-attendance.html'">
                                    <i class="fas fa-clock"></i>
                                    <span>Monitor Kehadiran</span>
                                </button>
                                <button class="quick-action-btn" onclick="handleAdminAction('backup')">
                                    <i class="fas fa-database"></i>
                                    <span>Backup Data</span>
                                </button>
                                <button class="quick-action-btn" onclick="handleAdminAction('audit')">
                                    <i class="fas fa-search"></i>
                                    <span>Audit Log</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Security & Access Control -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3><i class="fas fa-shield-alt"></i> Keamanan & Kontrol Akses</h3>
                        </div>
                        <div class="section-content">
                            <div class="settings-list">
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Autentikasi 2 Faktor</h4>
                                        <p>Tingkatkan keamanan dengan verifikasi 2 langkah</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Session Timeout</h4>
                                        <p>Waktu timeout otomatis untuk keamanan</p>
                                    </div>
                                    <select style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 6px;">
                                        <option value="30">30 menit</option>
                                        <option value="60" selected>1 jam</option>
                                        <option value="120">2 jam</option>
                                        <option value="240">4 jam</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Login Alerts</h4>
                                        <p>Notifikasi saat ada login dari perangkat baru</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>IP Whitelist</h4>
                                        <p>Kelola daftar IP yang diizinkan</p>
                                    </div>
                                    <button class="setting-btn">
                                        <i class="fas fa-list"></i> Kelola
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Settings -->
                    <div class="profile-section">
                        <div class="section-header">
                            <h3><i class="fas fa-cog"></i> Pengaturan Akun</h3>
                        </div>
                        <div class="section-content">
                            <div class="settings-list">
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Ubah Password</h4>
                                        <p>Perbarui password administrator</p>
                                    </div>
                                    <button class="setting-btn" id="changePasswordBtn">
                                        <i class="fas fa-key"></i> Ubah
                                    </button>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Notifikasi Email</h4>
                                        <p>Terima notifikasi sistem via email</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Notifikasi Push</h4>
                                        <p>Notifikasi real-time untuk admin</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Mode Gelap</h4>
                                        <p>Aktifkan tema gelap untuk admin panel</p>
                                    </div>
                                    <label class="toggle-switch">
                                        <input type="checkbox">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Bahasa Interface</h4>
                                        <p>Pilih bahasa untuk admin panel</p>
                                    </div>
                                    <select style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 6px;">
                                        <option value="id">Bahasa Indonesia</option>
                                        <option value="en">English</option>
                                    </select>
                                </div>
                                <div class="setting-item">
                                    <div class="setting-info">
                                        <h4>Zona Waktu Sistem</h4>
                                        <p>Atur zona waktu default sistem</p>
                                    </div>
                                    <select style="padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 6px;">
                                        <option value="wib">WIB (UTC+7)</option>
                                        <option value="wita">WITA (UTC+8)</option>
                                        <option value="wit">WIT (UTC+9)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Change Password Modal -->
    <div class="modal" id="passwordModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Ubah Password Administrator</h3>
                <button class="close-modal" id="closePasswordModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="password-form" id="passwordForm">
                <div class="form-group">
                    <label for="currentPassword">Password Saat Ini</label>
                    <input type="password" id="currentPassword" required>
                </div>
                <div class="form-group">
                    <label for="newPassword">Password Baru</label>
                    <input type="password" id="newPassword" required minlength="8">
                    <small>Minimal 8 karakter, kombinasi huruf, angka, dan simbol</small>
                </div>
                <div class="form-group">
                    <label for="confirmPassword">Konfirmasi Password Baru</label>
                    <input type="password" id="confirmPassword" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" id="cancelPassword">Batal</button>
                    <button type="submit" class="btn-primary">Simpan Password</button>
                </div>
            </form>
        </div>
    </div>

    <script src="../assets/dashboard.js"></script>
    <script src="../assets/admin-profile.js"></script>
</body>
</html>
