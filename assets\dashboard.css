:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --success-color: #16a34a;
    --danger-color: #dc2626;
    --warning-color: #ca8a04;
    --text-color: #1f2937;
    --text-light: #6b7280;
    --border-color: #e5e7eb;
    --background: #f3f4f6;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    background-color: var(--background);
    color: var(--text-color);
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: white;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--border-color);
}

.sidebar-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-header .logo {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.sidebar-header h2 {
    font-size: 1.25rem;
    color: var(--text-color);
}

.sidebar-nav {
    margin-top: 2rem;
    flex: 1;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.875rem 1rem;
    color: var(--text-light);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    padding-left: 1.2rem;
}

.sidebar-nav a span {
    vertical-align: middle;
    line-height: 1.2;
    text-align: left;
    display: inline-block;
    width: calc(100% - 1rem);
}

.sidebar-nav a:hover {
    background: var(--background);
    color: var(--primary-color);
}

.sidebar-nav a.active {
    background: var(--primary-color);
    color: white;
}

.sidebar-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
}

.sidebar-footer a {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.875rem 1rem;
    color: var(--danger-color);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar-footer a:hover {
    background: #fee2e2;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    padding: 2rem;
    padding-top: .8rem;
    overflow-y: auto;
}

/* Top Bar Styles */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    height: 4rem;
}

.search-bar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    width: 300px;
}

.search-bar input {
    border: none;
    outline: none;
    font-size: 0.95rem;
    width: 100%;
}

.search-bar i {
    color: var(--text-light);
}

.top-bar-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.notifications {
    position: relative;
    cursor: pointer;
}

.notifications i {
    font-size: 1.25rem;
    color: var(--text-light);
}

.badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--danger-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 999px;
}

.admin-profile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    font-size: 0.95rem;
    color: var(--text-color);
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.admin-profile:hover {
    opacity: 0.8;
}

.admin-profile img {
    width: 40px;
    height: 40px;
    border-radius: 999px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-details h3 {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.stat-change {
    font-size: 0.875rem;
    color: var(--text-light);
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* Chart Styles */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.chart-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.chart-header h3 {
    font-size: 1.1rem;
    color: var(--text-color);
    margin: 0;
}

.chart-header select {
    padding: 0.5rem 2rem 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    color: var(--text-color);
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.7rem center;
    background-size: 1em;
}

.chart-header select:hover {
    border-color: var(--primary-color);
}

.chart-header select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* Recent Activity */
.recent-activity {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h3 {
    font-size: 1.125rem;
}

.section-header button {
    padding: 0.5rem 1rem;
    background: var(--background);
    border: none;
    border-radius: 6px;
    color: var(--primary-color);
    font-size: 0.875rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.section-header button:hover {
    background: #e5e7eb;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    background: var(--background);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.activity-icon.check-in {
    background: var(--success-color);
}

.activity-icon.late {
    background: var(--warning-color);
}

.activity-icon.leave {
    background: var(--primary-color);
}

.activity-icon.check-out {
    background: var(--danger-color);
}

.activity-details {
    flex: 1;
}

.activity-details p {
    margin-bottom: 0.25rem;
}

.activity-details span {
    font-size: 0.875rem;
    color: var(--text-light);
}

.activity-status {
    padding: 0.375rem 0.75rem;
    border-radius: 999px;
    font-size: 0.875rem;
}

.activity-status.on-time {
    background: #dcfce7;
    color: var(--success-color);
}

.activity-status.late {
    background: #fef3c7;
    color: var(--warning-color);
}

.activity-status.pending {
    background: #e5e7eb;
    color: var(--text-light);
}

/* Employee Dashboard Specific Styles */
.date-time {
    text-align: center;
}

.date-time h2 {
    font-size: 2.5rem;
    margin-bottom: 0.25rem;
}

.date-time p {
    color: var(--text-light);
}

.attendance-action {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    text-align: center;
}

.attendance-status {
    margin-bottom: 1.5rem;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 999px;
    font-weight: 500;
    margin-top: 0.5rem;
}

.status-badge.present {
    background: #dcfce7;
    color: var(--success-color);
}

.attendance-times {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.time-card {
    text-align: center;
    padding: 1rem;
    background: var(--background);
    border-radius: 12px;
    min-width: 140px;
}

.time-card.pending {
    opacity: 0.7;
}

.time-card i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.time-details h4 {
    margin-bottom: 0.25rem;
    color: var(--text-light);
}

.time-details p {
    font-size: 1.25rem;
    font-weight: 600;
}

.action-button {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    background: var(--primary-color);
    color: white;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.action-button:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.action-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.schedule-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.schedule-card {
    padding: 1rem;
    background: var(--background);
    border-radius: 8px;
    position: relative;
}

.schedule-card.today {
    background: var(--primary-color);
    color: white;
}

.schedule-card.today .schedule-time,
.schedule-card.today .schedule-shift {
    color: rgba(255, 255, 255, 0.9);
}

.schedule-card.off {
    opacity: 0.7;
}

.schedule-date h4 {
    margin-bottom: 0.25rem;
}

.schedule-date p {
    font-size: 0.875rem;
    color: var(--text-light);
}

.schedule-details {
    margin-top: 1rem;
}

.schedule-time {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.schedule-shift {
    font-size: 0.875rem;
    color: var(--text-light);
}

.schedule-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
}

@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        padding: 1rem;
    }

    .main-content {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .search-bar {
        width: 200px;
    }
}

/* Profile Modal Styles */
.profile-modal {
    display: none; /* Hidden by default, shown via JS */
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
    justify-content: center;
    align-items: center;
}

.profile-modal-content {
    background: #fff;
    margin: 5% auto;
    border-radius: 12px;
    padding: 32px 28px 24px 28px;
    max-width: 400px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.18);
    position: relative;
    animation: fadeIn 0.2s;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.close-profile {
    position: absolute;
    top: 18px;
    right: 22px;
    font-size: 1.5rem;
    color: #888;
    cursor: pointer;
    transition: color 0.2s;
}
.close-profile:hover {
    color: #dc2626;
}

.profile-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 18px;
}
.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 10px;
    border: 3px solid #4f46e5;
    background: #f3f4f6;
}
.profile-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: #4f46e5;
}

.profile-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}
.profile-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}
.profile-group label {
    font-size: 0.98rem;
    color: #374151;
    margin-bottom: 2px;
}
.profile-group input {
    padding: 8px 10px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 1rem;
    background: #f9fafb;
    transition: border 0.2s;
}
.profile-group input:focus {
    border-color: #4f46e5;
    outline: none;
}
.save-profile-btn {
    background: #4f46e5;
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 10px 0;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 8px;
    transition: background 0.2s;
}
.save-profile-btn:hover {
    background: #3730a3;
}

@media (max-width: 500px) {
    .profile-modal-content {
        max-width: 95vw;
        padding: 18px 6vw 16px 6vw;
    }
    .profile-header h2 {
        font-size: 1.1rem;
    }
    .profile-avatar {
        width: 60px;
        height: 60px;
    }
}
