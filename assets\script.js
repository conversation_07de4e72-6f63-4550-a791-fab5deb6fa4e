// Show/hide password toggle
const passwordInput = document.getElementById('password');
const togglePassword = document.getElementById('togglePassword');
const loginForm = document.getElementById('loginForm');
const messageDiv = document.getElementById('message');

if (togglePassword) {
    togglePassword.addEventListener('click', () => {
        const type = passwordInput.type === 'password' ? 'text' : 'password';
        passwordInput.type = type;
        togglePassword.innerHTML = type === 'password' ? '<i class="fas fa-eye"></i>' : '<i class="fas fa-eye-slash"></i>';
    });
}

loginForm.addEventListener('submit', function(e) {
    e.preventDefault();
    messageDiv.textContent = '';
    messageDiv.className = 'message';
    const username = loginForm.username.value.trim();
    const password = loginForm.password.value.trim();
    if (!username || !password) {
        messageDiv.textContent = 'Please enter both username and password.';
        messageDiv.classList.add('error');
        return;
    }
    // Simulate login (replace with real authentication logic)
    if (username === 'admin' && password === 'admin') {
        messageDiv.textContent = 'Login successful!';
        messageDiv.classList.add('success');
        localStorage.setItem('userRole', 'admin');
        setTimeout(() => {
            window.location.href = 'pages/admin-dashboard.html';
        }, 800);
    } else if (username === 'employee' && password === 'employee') {
        messageDiv.textContent = 'Login successful!';
        messageDiv.classList.add('success');
        localStorage.setItem('userRole', 'employee');
        setTimeout(() => {
            window.location.href = 'pages/employee-dashboard.html';
        }, 800);
    } else {
        messageDiv.textContent = 'Invalid username or password.';
        messageDiv.classList.add('error');
    }
});
