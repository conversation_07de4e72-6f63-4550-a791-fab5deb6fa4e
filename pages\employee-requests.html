<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pengajuan - Sistem Absensi</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/dashboard.css">
    <link rel="stylesheet" href="../assets/employee-pages.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-user-clock"></i>
                </div>
                <h2>Sistem Absensi</h2>
            </div>
            <nav class="sidebar-nav">
                <a href="employee-dashboard.html">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
                <a href="employee-schedule.html">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Jadwal</span>
                </a>
                <a href="employee-reports.html">
                    <i class="fas fa-file-alt"></i>
                    <span>Laporan Saya</span>
                </a>
                <a href="employee-requests.html" class="active">
                    <i class="fas fa-envelope"></i>
                    <span>Pengajuan</span>
                </a>
                <a href="employee-profile.html">
                    <i class="fas fa-user"></i>
                    <span>Profil</span>
                </a>
            </nav>
            <div class="sidebar-footer">
                <a href="../index.html">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="date-time">
                    <h2 id="currentTime">08:00:00</h2>
                    <p id="currentDate">Senin, 19 Juni 2025</p>
                </div>
                <div class="top-bar-right">
                    <div class="notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">2</span>
                    </div>
                    <div class="admin-profile">
                        <img src="https://ui-avatars.com/api/?name=John+Doe&background=4f46e5&color=fff" alt="John Doe">
                        <span>John Doe</span>
                    </div>
                </div>
            </header>

            <!-- Requests Content -->
            <div class="dashboard-content">
                <div class="section-header">
                    <h3>Pengajuan Saya</h3>
                    <button id="newRequestBtn" class="action-button">
                        <i class="fas fa-plus"></i> Buat Pengajuan Baru
                    </button>
                </div>

                <!-- Request Stats -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: #4f46e5;">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="stat-details">
                            <h3>Total Pengajuan</h3>
                            <p class="stat-number">12</p>
                            <span class="stat-change">Tahun ini</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: #16a34a;">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-details">
                            <h3>Disetujui</h3>
                            <p class="stat-number">8</p>
                            <span class="stat-change">Tahun ini</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: #dc2626;">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-details">
                            <h3>Ditolak</h3>
                            <p class="stat-number">2</p>
                            <span class="stat-change">Tahun ini</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: #ca8a04;">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-details">
                            <h3>Menunggu</h3>
                            <p class="stat-number">2</p>
                            <span class="stat-change">Saat ini</span>
                        </div>
                    </div>
                </div>

                <!-- Request Form (Hidden by default, shown via JS) -->
                <div id="requestForm" class="request-form-container" style="display: none;">
                    <div class="request-form">
                        <div class="form-header">
                            <h3>Buat Pengajuan Baru</h3>
                            <button id="closeFormBtn" class="close-btn"><i class="fas fa-times"></i></button>
                        </div>
                        <form>
                            <div class="form-group">
                                <label for="requestType">Jenis Pengajuan</label>
                                <select id="requestType" required>
                                    <option value="" disabled selected>Pilih jenis pengajuan</option>
                                    <option value="leave">Cuti</option>
                                    <option value="sick">Sakit</option>
                                    <option value="permission">Izin</option>
                                    <option value="remote">Kerja Remote</option>
                                    <option value="overtime">Lembur</option>
                                </select>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="startDate">Tanggal Mulai</label>
                                    <input type="date" id="startDate" required>
                                </div>
                                <div class="form-group">
                                    <label for="endDate">Tanggal Selesai</label>
                                    <input type="date" id="endDate" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="reason">Alasan</label>
                                <textarea id="reason" rows="4" placeholder="Jelaskan alasan pengajuan Anda" required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="attachment">Lampiran (opsional)</label>
                                <input type="file" id="attachment">
                                <small>Format yang didukung: PDF, JPG, PNG (maks. 5MB)</small>
                            </div>
                            <div class="form-actions">
                                <button type="button" id="cancelRequestBtn" class="cancel-btn">Batal</button>
                                <button type="submit" class="submit-btn">Kirim Pengajuan</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Request List -->
                <div class="request-list-container">
                    <div class="request-filters">
                        <div class="filter-group">
                            <label for="statusFilter">Status:</label>
                            <select id="statusFilter">
                                <option value="all">Semua</option>
                                <option value="pending">Menunggu</option>
                                <option value="approved">Disetujui</option>
                                <option value="rejected">Ditolak</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="typeFilter">Jenis:</label>
                            <select id="typeFilter">
                                <option value="all">Semua</option>
                                <option value="leave">Cuti</option>
                                <option value="sick">Sakit</option>
                                <option value="permission">Izin</option>
                                <option value="remote">Kerja Remote</option>
                                <option value="overtime">Lembur</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="dateFilter">Periode:</label>
                            <select id="dateFilter">
                                <option value="all">Semua</option>
                                <option value="month">Bulan Ini</option>
                                <option value="quarter">3 Bulan Terakhir</option>
                                <option value="year">Tahun Ini</option>
                            </select>
                        </div>
                    </div>

                    <div class="request-table-container">
                        <table class="request-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Jenis</th>
                                    <th>Tanggal Pengajuan</th>
                                    <th>Periode</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>#REQ-001</td>
                                    <td>Cuti</td>
                                    <td>15 Juni 2025</td>
                                    <td>22-24 Juni 2025</td>
                                    <td><span class="request-status approved">Disetujui</span></td>
                                    <td>
                                        <button class="action-icon view-btn"><i class="fas fa-eye"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>#REQ-002</td>
                                    <td>Sakit</td>
                                    <td>10 Juni 2025</td>
                                    <td>10-11 Juni 2025</td>
                                    <td><span class="request-status approved">Disetujui</span></td>
                                    <td>
                                        <button class="action-icon view-btn"><i class="fas fa-eye"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>#REQ-003</td>
                                    <td>Kerja Remote</td>
                                    <td>18 Juni 2025</td>
                                    <td>25 Juni 2025</td>
                                    <td><span class="request-status pending">Menunggu</span></td>
                                    <td>
                                        <button class="action-icon view-btn"><i class="fas fa-eye"></i></button>
                                        <button class="action-icon edit-btn"><i class="fas fa-edit"></i></button>
                                        <button class="action-icon delete-btn"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>#REQ-004</td>
                                    <td>Lembur</td>
                                    <td>12 Juni 2025</td>
                                    <td>12 Juni 2025</td>
                                    <td><span class="request-status rejected">Ditolak</span></td>
                                    <td>
                                        <button class="action-icon view-btn"><i class="fas fa-eye"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>#REQ-005</td>
                                    <td>Izin</td>
                                    <td>19 Juni 2025</td>
                                    <td>20 Juni 2025</td>
                                    <td><span class="request-status pending">Menunggu</span></td>
                                    <td>
                                        <button class="action-icon view-btn"><i class="fas fa-eye"></i></button>
                                        <button class="action-icon edit-btn"><i class="fas fa-edit"></i></button>
                                        <button class="action-icon delete-btn"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="pagination">
                        <button class="pagination-btn" disabled><i class="fas fa-chevron-left"></i></button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn"><i class="fas fa-chevron-right"></i></button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Request Detail Modal -->
    <div id="requestDetailModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Detail Pengajuan #REQ-001</h3>
                <button class="close-modal"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="request-detail-grid">
                    <div class="detail-group">
                        <label>Jenis Pengajuan</label>
                        <p>Cuti</p>
                    </div>
                    <div class="detail-group">
                        <label>Status</label>
                        <p><span class="request-status approved">Disetujui</span></p>
                    </div>
                    <div class="detail-group">
                        <label>Tanggal Pengajuan</label>
                        <p>15 Juni 2025</p>
                    </div>
                    <div class="detail-group">
                        <label>Tanggal Mulai</label>
                        <p>22 Juni 2025</p>
                    </div>
                    <div class="detail-group">
                        <label>Tanggal Selesai</label>
                        <p>24 Juni 2025</p>
                    </div>
                    <div class="detail-group">
                        <label>Durasi</label>
                        <p>3 hari</p>
                    </div>
                    <div class="detail-group full-width">
                        <label>Alasan</label>
                        <p>Keperluan keluarga yang tidak bisa ditinggalkan.</p>
                    </div>
                    <div class="detail-group full-width">
                        <label>Lampiran</label>
                        <p><a href="#" class="attachment-link"><i class="fas fa-file-pdf"></i> dokumen_pendukung.pdf</a></p>
                    </div>
                    <div class="detail-group full-width">
                        <label>Catatan Persetujuan</label>
                        <p>Disetujui oleh supervisor. Pastikan pekerjaan sudah diserahterimakan sebelum cuti.</p>
                    </div>
                </div>
                <div class="request-timeline">
                    <h4>Riwayat Status</h4>
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-icon"><i class="fas fa-paper-plane"></i></div>
                            <div class="timeline-content">
                                <h5>Pengajuan Dibuat</h5>
                                <p>15 Juni 2025, 10:30 WIB</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-icon"><i class="fas fa-eye"></i></div>
                            <div class="timeline-content">
                                <h5>Ditinjau oleh Supervisor</h5>
                                <p>16 Juni 2025, 09:15 WIB</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-icon"><i class="fas fa-check"></i></div>
                            <div class="timeline-content">
                                <h5>Disetujui</h5>
                                <p>16 Juni 2025, 14:45 WIB</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="close-btn">Tutup</button>
            </div>
        </div>
    </div>

    <script src="../assets/dashboard.js"></script>
    <script>
        // Toggle request form visibility
        document.getElementById('newRequestBtn').addEventListener('click', function() {
            document.getElementById('requestForm').style.display = 'flex';
        });

        document.getElementById('closeFormBtn').addEventListener('click', function() {
            document.getElementById('requestForm').style.display = 'none';
        });

        document.getElementById('cancelRequestBtn').addEventListener('click', function() {
            document.getElementById('requestForm').style.display = 'none';
        });

        // View request detail modal
        const viewButtons = document.querySelectorAll('.view-btn');
        const requestModal = document.getElementById('requestDetailModal');
        const closeModalButtons = document.querySelectorAll('.close-modal, .modal-footer .close-btn');

        viewButtons.forEach(button => {
            button.addEventListener('click', function() {
                requestModal.style.display = 'block';
            });
        });

        closeModalButtons.forEach(button => {
            button.addEventListener('click', function() {
                requestModal.style.display = 'none';
            });
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === requestModal) {
                requestModal.style.display = 'none';
            }
        });
    </script>
</body>
</html>
