// Load Chart.js and Chart.js Annotation plugin dynamically
function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

// Initialize charts when DOM is loaded and scripts are loaded
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Load Chart.js first
        await loadScript('https://cdn.jsdelivr.net/npm/chart.js');
        // Then load the annotation plugin
        await loadScript('https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation');
        // Initialize charts after scripts are loaded
        initializeCharts();
    } catch (error) {
        console.error('Failed to load Chart.js:', error);
    }
});

// Global chart instances
let attendanceChart;
let departmentChart;

// Initialize both charts
function initializeCharts() {
    initializeAttendanceChart();
    initializeDepartmentChart();
}

// Initialize Attendance Chart
function initializeAttendanceChart() {
    const ctx = document.getElementById('attendanceChart').getContext('2d');
    
    // Sample data
    const data = {
        labels: ['Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min'],
        datasets: [
            {
                label: 'Hadir',
                data: [142, 145, 141, 143, 140, 0, 0],
                borderColor: '#16a34a',
                backgroundColor: 'rgba(22, 163, 74, 0.1)',
                fill: true,
            },
            {
                label: 'Terlambat',
                data: [8, 5, 7, 6, 9, 0, 0],
                borderColor: '#ca8a04',
                backgroundColor: 'rgba(202, 138, 4, 0.1)',
                fill: true,
            },
            {
                label: 'Tidak Hadir',
                data: [6, 6, 8, 7, 7, 0, 0],
                borderColor: '#dc2626',
                backgroundColor: 'rgba(220, 38, 38, 0.1)',
                fill: true,
            }
        ]
    };

    // Chart configuration with weekend annotations
    const config = {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    position: 'top'
                },
                tooltip: {
                    position: 'nearest',
                    callbacks: {
                        beforeTitle: function(context) {
                            const index = context[0].dataIndex;
                            if (index >= 5) { // Weekend days
                                return 'LIBUR';
                            }
                            return null;
                        }
                    }
                },
                annotation: {
                    annotations: {
                        weekend1: {
                            type: 'box',
                            xMin: 5,
                            xMax: 6,
                            backgroundColor: 'rgba(220, 220, 220, 0.2)',
                            borderColor: 'rgba(220, 220, 220, 0.5)',
                            label: {
                                display: true,
                                content: 'Libur',
                                position: 'start',
                                font: {
                                    style: 'italic'
                                },
                                color: '#666'
                            }
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    stacked: false
                }
            }
        }
    };

    attendanceChart = new Chart(ctx, config);
}

// Initialize Department Chart
function initializeDepartmentChart() {
    const ctx = document.getElementById('departmentChart').getContext('2d');
    
    // Sample data
    const data = {
        labels: ['IT', 'HR', 'Keuangan', 'Marketing', 'Operasional', 'Sales'],
        datasets: [{
            data: [30, 15, 20, 25, 35, 31],
            backgroundColor: [
                '#4f46e5',
                '#16a34a',
                '#ca8a04',
                '#dc2626',
                '#0891b2',
                '#7c3aed'
            ]
        }]
    };

    departmentChart = new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
}

// Update Attendance Chart based on selected period
function updateAttendanceChart() {
    const period = document.getElementById('attendance-period').value;
    
    let labels, data;
    
    if (period === '7') {
        labels = ['Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min'];
        data = generateWeekData();
    } else if (period === '30') {
        // Generate last 30 days data
        labels = generateMonthDayLabels(30);
        data = generateMonthData(30);
    } else {
        // Generate 3 months data with week labels
        labels = Array.from({length: 12}, (_, i) => `Minggu ${i + 1}`);
        data = {
            hadir: Array.from({length: 12}, () => Math.floor(Math.random() * (145 - 135) + 135)),
            terlambat: Array.from({length: 12}, () => Math.floor(Math.random() * 10)),
            tidakHadir: Array.from({length: 12}, () => Math.floor(Math.random() * 8))
        };
    }

    // Update annotations for 30-day view
    if (period === '30') {
        updateWeekendAnnotations(30);
    }

    attendanceChart.data.labels = labels;
    attendanceChart.data.datasets[0].data = data.hadir;
    attendanceChart.data.datasets[1].data = data.terlambat;
    attendanceChart.data.datasets[2].data = data.tidakHadir;
    attendanceChart.update();
}

// Update Department Chart based on selected view
function updateDepartmentChart() {
    const view = document.getElementById('department-view').value;
    
    // Sample data update logic (in real app, this would fetch from backend)
    let data;
    
    if (view === 'employees') {
        data = [30, 15, 20, 25, 35, 31];
    } else {
        // Attendance percentage
        data = [95, 92, 88, 91, 93, 90];
    }

    departmentChart.data.datasets[0].data = data;
    departmentChart.update();
}

// Helper function to generate week data with weekends as zero
function generateWeekData() {
    return {
        hadir: [142, 145, 141, 143, 140, 0, 0],
        terlambat: [8, 5, 7, 6, 9, 0, 0],
        tidakHadir: [6, 6, 8, 7, 7, 0, 0]
    };
}

// Helper function to generate month day labels with proper day names
function generateMonthDayLabels(days) {
    const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];
    const today = new Date();
    return Array.from({length: days}, (_, i) => {
        const date = new Date(today);
        date.setDate(today.getDate() - (days - 1 - i));
        return dayNames[date.getDay()];
    });
}

// Helper function to generate month data with weekends as zero
function generateMonthData(days) {
    const today = new Date();
    const hadir = [];
    const terlambat = [];
    const tidakHadir = [];

    for (let i = 0; i < days; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() - (days - 1 - i));
        const isWeekend = date.getDay() === 0 || date.getDay() === 6;

        if (isWeekend) {
            hadir.push(0);
            terlambat.push(0);
            tidakHadir.push(0);
        } else {
            hadir.push(Math.floor(Math.random() * (145 - 135) + 135));
            terlambat.push(Math.floor(Math.random() * 10));
            tidakHadir.push(Math.floor(Math.random() * 8));
        }
    }

    return { hadir, terlambat, tidakHadir };
}

// Helper function to update weekend annotations for 30-day view
function updateWeekendAnnotations(days) {
    const annotations = {};
    const today = new Date();

    for (let i = 0; i < days; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() - (days - 1 - i));
        if (date.getDay() === 0 || date.getDay() === 6) {
            annotations[`weekend${i}`] = {
                type: 'box',
                xMin: i - 0.5,
                xMax: i + 0.5,
                backgroundColor: 'rgba(220, 220, 220, 0.2)',
                borderColor: 'rgba(220, 220, 220, 0.5)',
            };
        }
    }

    attendanceChart.options.plugins.annotation.annotations = annotations;
}
