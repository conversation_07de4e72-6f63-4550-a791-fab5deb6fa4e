console.log("admin-pages.js loaded and executing");

// Modal Functions - Make them globally available
window.showModal = function(modalId) {
    console.log("showModal called with modalId:", modalId);
    try {
        const modal = document.getElementById(modalId);
        if (modal) {
            console.log("Modal found, adding show class");
            console.log("Modal current classes:", modal.className);
            modal.classList.add('show');
            console.log("Modal classes after adding show:", modal.className);
            document.body.style.overflow = 'hidden';
            console.log("Modal should now be visible");
        } else {
            console.error("Modal not found with id:", modalId);
            console.log("Available modals:", document.querySelectorAll('.modal'));
        }
    } catch (error) {
        console.error("Error in showModal:", error);
    }
}

window.hideModal = function(modalId) {
    console.log("hideModal called with modalId:", modalId);
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = '';
    }
}

// Close modal when clicking outside
document.addEventListener('click', (e) => {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }
    });
});

// Table Selection
const selectAllCheckbox = document.getElementById('selectAll');
if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', (e) => {
        const checkboxes = document.querySelectorAll('.data-table tbody input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = e.target.checked;
        });
    });
}

// Search functionality
const adminSearchInput = document.querySelector('.search-bar input');
if (adminSearchInput) {
    adminSearchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        const tableRows = document.querySelectorAll('.data-table tbody tr');

        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
}

// Filter functionality
const filterSelects = document.querySelectorAll('.filter-group select');
filterSelects.forEach(select => {
    select.addEventListener('change', () => {
        // Add filter logic here
        console.log('Filter changed:', select.value);
    });
});

// Date picker for attendance page
const attendanceDatePicker = document.getElementById('attendanceDate');
if (attendanceDatePicker) {
    const today = new Date().toISOString().split('T')[0];
    attendanceDatePicker.value = today;
    
    attendanceDatePicker.addEventListener('change', (e) => {
        // Add date change logic here
        console.log('Selected date:', e.target.value);
    });
}

 // Form submission handling
const forms = document.querySelectorAll('.modal-form');
forms.forEach(form => {
    form.addEventListener('submit', (e) => {
        e.preventDefault();
        // Add form submission logic here
        console.log('Form submitted');
        
        // Get form data
        const formData = new FormData(form);
        const employeeData = {};
        formData.forEach((value, key) => {
            employeeData[key] = value;
        });
        
        // Check if it's the add employee form
        const modal = form.closest('.modal');
        if (modal && modal.id === 'addEmployeeModal') {
            // Use API to add employee
            addEmployeeAPI(formData);
        }
        
        // Close the modal
        if (modal) {
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }
    });
});

// Export functionality
const exportButton = document.querySelector('.filter-button i.fa-download');
if (exportButton) {
    exportButton.parentElement.addEventListener('click', () => {
        // Add export logic here
        console.log('Exporting data...');
    });
}

// Animation for table rows
function animateTableRows() {
    const rows = document.querySelectorAll('.data-table tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(10px)';
        row.style.transition = 'all 0.3s ease';
        
        setTimeout(() => {
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
}

// Run animations when page loads
document.addEventListener('DOMContentLoaded', () => {
    animateTableRows();

    // Add event listener for the add employee button
    const addButton = document.getElementById('addEmployeeBtn');
    if (addButton) {
        console.log("Add button found, attaching event listener");
        addButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log("Add button clicked via event listener");
            showModal('addEmployeeModal');
        });
    } else {
        console.error("Add button not found with ID 'addEmployeeBtn'");
        // Fallback to class selector
        const addButtonFallback = document.querySelector('.add-button');
        if (addButtonFallback) {
            console.log("Add button found via class selector, attaching event listener");
            addButtonFallback.addEventListener('click', function(e) {
                e.preventDefault();
                console.log("Add button clicked via fallback event listener");
                showModal('addEmployeeModal');
            });
        }
    }

    // Add event listener for the close modal button
    const closeButton = document.getElementById('closeEmployeeModalBtn');
    if (closeButton) {
        console.log("Close button found, attaching event listener");
        closeButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log("Close button clicked");
            hideModal('addEmployeeModal');
        });
    }

    // Add event listener for the cancel button
    const cancelButton = document.getElementById('cancelEmployeeBtn');
    if (cancelButton) {
        console.log("Cancel button found, attaching event listener");
        cancelButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log("Cancel button clicked");
            hideModal('addEmployeeModal');
        });
    }
});

// API Integration Functions
async function addEmployeeAPI(formData) {
    try {
        // Prepare employee data
        const employeeData = {
            employee_id: formData.get('employeeId'),
            full_name: formData.get('fullName'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            department_id: formData.get('department'),
            position: formData.get('position'),
            hire_date: formData.get('hireDate'),
            salary: formData.get('salary') ? parseFloat(formData.get('salary')) : null,
            address: formData.get('address')
        };

        // Show loading state
        const submitBtn = document.querySelector('#addEmployeeModal button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        showLoading(submitBtn, 'Adding Employee...');

        // Call API
        const response = await window.apiClient.createEmployee(employeeData);

        // Show success message
        showNotification('Employee added successfully!', 'success');

        // Reset form and close modal
        const form = document.querySelector('#addEmployeeModal form');
        form.reset();
        hideModal('addEmployeeModal');

        // Refresh employee list
        loadEmployees();

        hideLoading(submitBtn, originalText);

    } catch (error) {
        console.error('Error adding employee:', error);
        showNotification(`Failed to add employee: ${error.message}`, 'error');

        // Reset button state
        const submitBtn = document.querySelector('#addEmployeeModal button[type="submit"]');
        if (submitBtn) {
            hideLoading(submitBtn, 'Add Employee');
        }
    }
}

async function loadEmployees() {
    try {
        const response = await window.apiClient.getEmployees();
        const tbody = document.querySelector('.data-table tbody');

        if (!tbody) return;

        // Clear existing rows
        tbody.innerHTML = '';

        // Add new rows
        response.employees.forEach(employee => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><input type="checkbox" data-employee-id="${employee.id}"></td>
                <td>${employee.employee_id}</td>
                <td>
                    <div class="employee-info">
                        <img src="https://ui-avatars.com/api/?name=${encodeURIComponent(employee.full_name)}&background=4f46e5&color=fff" alt="${employee.full_name}">
                        <span>${employee.full_name}</span>
                    </div>
                </td>
                <td>${employee.department_name || 'N/A'}</td>
                <td>${employee.position}</td>
                <td>${employee.email}</td>
                <td><span class="status-badge ${employee.is_active ? 'active' : 'inactive'}">${employee.is_active ? 'Aktif' : 'Tidak Aktif'}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit" onclick="editEmployee('${employee.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn view" onclick="viewEmployee('${employee.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteEmployee('${employee.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });

        // Re-run animation for the new rows
        animateTableRows();

    } catch (error) {
        console.error('Error loading employees:', error);
        showNotification(`Failed to load employees: ${error.message}`, 'error');
    }
}

async function editEmployee(employeeId) {
    try {
        const response = await window.apiClient.getEmployee(employeeId);
        const employee = response.employee;

        // Populate edit form with employee data
        const form = document.querySelector('#editEmployeeModal form');
        if (form) {
            form.querySelector('[name="employeeId"]').value = employee.employee_id;
            form.querySelector('[name="fullName"]').value = employee.full_name;
            form.querySelector('[name="email"]').value = employee.email;
            form.querySelector('[name="phone"]').value = employee.phone || '';
            form.querySelector('[name="department"]').value = employee.department_id || '';
            form.querySelector('[name="position"]').value = employee.position;
            form.querySelector('[name="hireDate"]').value = employee.hire_date ? employee.hire_date.split('T')[0] : '';
            form.querySelector('[name="salary"]').value = employee.salary || '';
            form.querySelector('[name="address"]').value = employee.address || '';

            // Store employee ID for update
            form.dataset.employeeId = employeeId;
        }

        showModal('editEmployeeModal');

    } catch (error) {
        console.error('Error loading employee for edit:', error);
        showNotification(`Failed to load employee data: ${error.message}`, 'error');
    }
}

async function viewEmployee(employeeId) {
    try {
        const response = await window.apiClient.getEmployee(employeeId);
        const employee = response.employee;

        // Populate view modal with employee data
        const modal = document.querySelector('#viewEmployeeModal');
        if (modal) {
            modal.querySelector('.employee-name').textContent = employee.full_name;
            modal.querySelector('.employee-id').textContent = employee.employee_id;
            modal.querySelector('.employee-email').textContent = employee.email;
            modal.querySelector('.employee-phone').textContent = employee.phone || 'N/A';
            modal.querySelector('.employee-department').textContent = employee.department_name || 'N/A';
            modal.querySelector('.employee-position').textContent = employee.position;
            modal.querySelector('.employee-hire-date').textContent = employee.hire_date ? new Date(employee.hire_date).toLocaleDateString('id-ID') : 'N/A';
            modal.querySelector('.employee-salary').textContent = employee.salary ? `Rp ${employee.salary.toLocaleString('id-ID')}` : 'N/A';
            modal.querySelector('.employee-status').textContent = employee.is_active ? 'Aktif' : 'Tidak Aktif';
        }

        showModal('viewEmployeeModal');

    } catch (error) {
        console.error('Error loading employee for view:', error);
        showNotification(`Failed to load employee data: ${error.message}`, 'error');
    }
}

async function deleteEmployee(employeeId) {
    // Use a more user-friendly confirmation approach
    const userConfirmed = await showConfirmDialog(
        'Hapus Karyawan',
        'Apakah Anda yakin ingin menghapus karyawan ini? Tindakan ini tidak dapat dibatalkan.'
    );

    if (userConfirmed) {
        try {
            await window.apiClient.deleteEmployee(employeeId);
            showNotification('Employee deleted successfully!', 'success');
            loadEmployees();
        } catch (error) {
            console.error('Error deleting employee:', error);
            showNotification(`Failed to delete employee: ${error.message}`, 'error');
        }
    }
}

// Custom confirmation dialog function
function showConfirmDialog(title, message) {
    return new Promise((resolve) => {
        // Create a custom modal for confirmation
        const confirmModal = document.createElement('div');
        confirmModal.className = 'modal show';
        confirmModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn secondary" id="confirmCancel">Batal</button>
                    <button type="button" class="btn danger" id="confirmDelete">Hapus</button>
                </div>
            </div>
        `;

        document.body.appendChild(confirmModal);
        document.body.style.overflow = 'hidden';

        // Handle button clicks
        document.getElementById('confirmCancel').onclick = () => {
            document.body.removeChild(confirmModal);
            document.body.style.overflow = '';
            resolve(false);
        };

        document.getElementById('confirmDelete').onclick = () => {
            document.body.removeChild(confirmModal);
            document.body.style.overflow = '';
            resolve(true);
        };

        // Close on backdrop click
        confirmModal.onclick = (e) => {
            if (e.target === confirmModal) {
                document.body.removeChild(confirmModal);
                document.body.style.overflow = '';
                resolve(false);
            }
        };
    });
}

// Load employees when page loads
if (window.location.pathname.includes('admin-employee')) {
    document.addEventListener('DOMContentLoaded', () => {
        loadEmployees();
    });
}
