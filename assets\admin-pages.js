console.log("admin-pages.js loaded and executing");


// Modal Functions
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = '';
    }
}

// Close modal when clicking outside
document.addEventListener('click', (e) => {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }
    });
});

// Table Selection
const selectAllCheckbox = document.getElementById('selectAll');
if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', (e) => {
        const checkboxes = document.querySelectorAll('.data-table tbody input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = e.target.checked;
        });
    });
}

// Search functionality
const searchInput = document.querySelector('.search-bar input');
if (searchInput) {
    searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        const tableRows = document.querySelectorAll('.data-table tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
}

// Filter functionality
const filterSelects = document.querySelectorAll('.filter-group select');
filterSelects.forEach(select => {
    select.addEventListener('change', () => {
        // Add filter logic here
        console.log('Filter changed:', select.value);
    });
});

// Date picker for attendance page
const attendanceDatePicker = document.getElementById('attendanceDate');
if (attendanceDatePicker) {
    const today = new Date().toISOString().split('T')[0];
    attendanceDatePicker.value = today;
    
    attendanceDatePicker.addEventListener('change', (e) => {
        // Add date change logic here
        console.log('Selected date:', e.target.value);
    });
}

 // Form submission handling
const forms = document.querySelectorAll('.modal-form');
forms.forEach(form => {
    form.addEventListener('submit', (e) => {
        e.preventDefault();
        // Add form submission logic here
        console.log('Form submitted');
        
        // Get form data
        const formData = new FormData(form);
        const employeeData = {};
        formData.forEach((value, key) => {
            employeeData[key] = value;
        });
        
        // Check if it's the add employee form
        const modal = form.closest('.modal');
        if (modal && modal.id === 'addEmployeeModal') {
            // Generate a new employee ID (simple increment for demo)
            const lastId = document.querySelector('.data-table tbody tr:last-child td:nth-child(2)')?.textContent || 'EMP000';
            const newIdNum = parseInt(lastId.replace('EMP', '')) + 1;
            const newId = `EMP${newIdNum.toString().padStart(3, '0')}`;
            
            // Create new row
            const tbody = document.querySelector('.data-table tbody');
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td><input type="checkbox"></td>
                <td>${newId}</td>
                <td>
                    <div class="employee-info">
                        <img src="https://ui-avatars.com/api/?name=${encodeURIComponent(formData.get('fullName'))}&background=4f46e5&color=fff" alt="${formData.get('fullName')}">
                        <span>${formData.get('fullName')}</span>
                    </div>
                </td>
                <td>${formData.get('department')}</td>
                <td>${formData.get('position')}</td>
                <td>${formData.get('email')}</td>
                <td><span class="status-badge active">Aktif</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit" onclick="showModal('editEmployeeModal')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn view" onclick="showModal('viewEmployeeModal')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn delete" onclick="showModal('deleteEmployeeModal')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(newRow);
            
            // Re-run animation for the new row
            animateTableRows();
        }
        
        // Close the modal
        if (modal) {
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }
    });
});

// Export functionality
const exportButton = document.querySelector('.filter-button i.fa-download');
if (exportButton) {
    exportButton.parentElement.addEventListener('click', () => {
        // Add export logic here
        console.log('Exporting data...');
    });
}

// Animation for table rows
function animateTableRows() {
    const rows = document.querySelectorAll('.data-table tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(10px)';
        row.style.transition = 'all 0.3s ease';
        
        setTimeout(() => {
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
}

// Run animations when page loads
document.addEventListener('DOMContentLoaded', () => {
    animateTableRows();
});
