console.log("admin-pages.js loaded and executing");

// Modal Functions - Make them globally available
window.showModal = function(modalId) {
    console.log("showModal called with modalId:", modalId);
    try {
        const modal = document.getElementById(modalId);
        if (modal) {
            console.log("Modal found, adding show class");
            console.log("Modal current classes:", modal.className);
            modal.classList.add('show');
            console.log("Modal classes after adding show:", modal.className);
            document.body.style.overflow = 'hidden';
            console.log("Modal should now be visible");
        } else {
            console.error("Modal not found with id:", modalId);
            console.log("Available modals:", document.querySelectorAll('.modal'));
        }
    } catch (error) {
        console.error("Error in showModal:", error);
    }
}

window.hideModal = function(modalId) {
    console.log("hideModal called with modalId:", modalId);
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = '';
    }
}

// Close modal when clicking outside
document.addEventListener('click', (e) => {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }
    });
});

// Table Selection
const selectAllCheckbox = document.getElementById('selectAll');
if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener('change', (e) => {
        const checkboxes = document.querySelectorAll('.data-table tbody input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = e.target.checked;
        });
    });
}

// Search functionality
const searchInput = document.querySelector('.search-bar input');
if (searchInput) {
    searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        const tableRows = document.querySelectorAll('.data-table tbody tr');
        
        tableRows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
}

// Filter functionality
const filterSelects = document.querySelectorAll('.filter-group select');
filterSelects.forEach(select => {
    select.addEventListener('change', () => {
        // Add filter logic here
        console.log('Filter changed:', select.value);
    });
});

// Date picker for attendance page
const attendanceDatePicker = document.getElementById('attendanceDate');
if (attendanceDatePicker) {
    const today = new Date().toISOString().split('T')[0];
    attendanceDatePicker.value = today;
    
    attendanceDatePicker.addEventListener('change', (e) => {
        // Add date change logic here
        console.log('Selected date:', e.target.value);
    });
}

 // Form submission handling
const forms = document.querySelectorAll('.modal-form');
forms.forEach(form => {
    form.addEventListener('submit', (e) => {
        e.preventDefault();
        // Add form submission logic here
        console.log('Form submitted');
        
        // Get form data
        const formData = new FormData(form);
        const employeeData = {};
        formData.forEach((value, key) => {
            employeeData[key] = value;
        });
        
        // Check if it's the add employee form
        const modal = form.closest('.modal');
        if (modal && modal.id === 'addEmployeeModal') {
            // Generate a new employee ID (simple increment for demo)
            const lastId = document.querySelector('.data-table tbody tr:last-child td:nth-child(2)')?.textContent || 'EMP000';
            const newIdNum = parseInt(lastId.replace('EMP', '')) + 1;
            const newId = `EMP${newIdNum.toString().padStart(3, '0')}`;
            
            // Create new row
            const tbody = document.querySelector('.data-table tbody');
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td><input type="checkbox"></td>
                <td>${newId}</td>
                <td>
                    <div class="employee-info">
                        <img src="https://ui-avatars.com/api/?name=${encodeURIComponent(formData.get('fullName'))}&background=4f46e5&color=fff" alt="${formData.get('fullName')}">
                        <span>${formData.get('fullName')}</span>
                    </div>
                </td>
                <td>${formData.get('department')}</td>
                <td>${formData.get('position')}</td>
                <td>${formData.get('email')}</td>
                <td><span class="status-badge active">Aktif</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="action-btn edit" onclick="showModal('editEmployeeModal')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn view" onclick="showModal('viewEmployeeModal')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn delete" onclick="showModal('deleteEmployeeModal')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(newRow);
            
            // Re-run animation for the new row
            animateTableRows();
        }
        
        // Close the modal
        if (modal) {
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }
    });
});

// Export functionality
const exportButton = document.querySelector('.filter-button i.fa-download');
if (exportButton) {
    exportButton.parentElement.addEventListener('click', () => {
        // Add export logic here
        console.log('Exporting data...');
    });
}

// Animation for table rows
function animateTableRows() {
    const rows = document.querySelectorAll('.data-table tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(10px)';
        row.style.transition = 'all 0.3s ease';
        
        setTimeout(() => {
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 50);
    });
}

// Run animations when page loads
document.addEventListener('DOMContentLoaded', () => {
    animateTableRows();

    // Add event listener for the add employee button
    const addButton = document.getElementById('addEmployeeBtn');
    if (addButton) {
        console.log("Add button found, attaching event listener");
        addButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log("Add button clicked via event listener");
            showModal('addEmployeeModal');
        });
    } else {
        console.error("Add button not found with ID 'addEmployeeBtn'");
        // Fallback to class selector
        const addButtonFallback = document.querySelector('.add-button');
        if (addButtonFallback) {
            console.log("Add button found via class selector, attaching event listener");
            addButtonFallback.addEventListener('click', function(e) {
                e.preventDefault();
                console.log("Add button clicked via fallback event listener");
                alert("Button clicked via fallback! Opening modal...");
                showModal('addEmployeeModal');
            });
        }
    }

    // Add event listener for the close modal button
    const closeButton = document.getElementById('closeEmployeeModalBtn');
    if (closeButton) {
        console.log("Close button found, attaching event listener");
        closeButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log("Close button clicked");
            hideModal('addEmployeeModal');
        });
    }

    // Add event listener for the cancel button
    const cancelButton = document.getElementById('cancelEmployeeBtn');
    if (cancelButton) {
        console.log("Cancel button found, attaching event listener");
        cancelButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log("Cancel button clicked");
            hideModal('addEmployeeModal');
        });
    }
});
