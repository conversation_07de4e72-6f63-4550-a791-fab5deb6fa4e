# Perbandingan Employee Profile vs Admin Profile

## Overview
Sistem absensi kini memiliki dua halaman profil yang berbeda, disesuaikan dengan kebutuhan dan hak akses masing-masing role.

## 📊 Perbandingan Fitur

| Aspek | Employee Profile | Admin Profile |
|-------|------------------|---------------|
| **File** | `employee-profile.html` | `admin-profile.html` |
| **JavaScript** | `employee-profile.js` | `admin-profile.js` |
| **Color Scheme** | Blue (#4f46e5) | Dark Blue (#4338ca) |
| **Target User** | <PERSON><PERSON><PERSON> | Administrator |

## 🎨 Perbedaan Visual

### Employee Profile
- **Warna <PERSON>a**: Biru standar (#4f46e5)
- **Avatar Background**: Biru standar
- **Header Cover**: Gradient biru standar
- **Tone**: Friendly, personal

### Admin Profile
- **<PERSON><PERSON>**: Biru gelap (#4338ca)
- **Avatar Background**: Biru gelap
- **Header Cover**: Gradient biru gelap ke indigo
- **Tone**: Professional, authoritative
- **Konsistensi**: Tetap menggunakan tema biru website

## 📋 Perbedaan Konten

### 1. Statistik Header

#### Employee Profile
- **Hari Hadir**: 22 hari
- **Jam Kerja**: 164 jam
- **Sisa Cuti**: 10 hari
- **Kehadiran**: 95%

#### Admin Profile
- **Total Karyawan**: 150 orang
- **Departemen**: 25 departemen
- **Pending Requests**: 12 pengajuan
- **System Uptime**: 98%

### 2. Informasi Pribadi

#### Employee Profile
- Fokus pada data personal karyawan
- Informasi kontak dan alamat
- Data demografis

#### Admin Profile
- Data personal administrator
- Informasi kontak khusus admin
- Identitas sistem administrator

### 3. Informasi Role-Specific

#### Employee Profile: "Informasi Kepegawaian"
- ID Karyawan
- Jabatan & Departemen
- Tanggal bergabung
- Atasan langsung
- Jam kerja & lokasi

#### Admin Profile: "Informasi Administrator"
- ID Administrator
- **Level Akses**: Super Administrator
- Hak akses sistem
- Terakhir login
- IP Address
- Session info

### 4. Section Khusus

#### Employee Profile: "Performa Kehadiran"
- Tingkat kehadiran
- Total hari kerja
- Keterlambatan
- Lembur
- Cuti digunakan/sisa

#### Admin Profile: "Manajemen Sistem"
- Total karyawan dikelola
- Departemen aktif
- Pending approvals
- System uptime
- Database size
- Backup terakhir

### 5. Quick Actions

#### Employee Profile
- **Ajukan Cuti** → employee-requests.html
- **Izin Terlambat** → employee-requests.html
- **Unduh Laporan** → employee-reports.html
- **Lihat Jadwal** → employee-schedule.html

#### Admin Profile
- **Kelola Karyawan** → admin-employees.html
- **Lihat Laporan** → admin-reports.html
- **Pengaturan Sistem** → admin-settings.html
- **Monitor Kehadiran** → admin-attendance.html
- **Backup Data** → JavaScript function
- **Audit Log** → JavaScript function

### 6. Pengaturan Keamanan

#### Employee Profile
- Pengaturan dasar
- Notifikasi personal
- Preferensi bahasa/zona waktu

#### Admin Profile: "Keamanan & Kontrol Akses"
- **Autentikasi 2 Faktor** ✅
- **Session Timeout** (30min - 4jam)
- **Login Alerts** ✅
- **IP Whitelist Management**

## 🔧 Perbedaan Teknis

### JavaScript Functions

#### Employee Profile (`employee-profile.js`)
```javascript
- initializeEmployeeProfile()
- updateProfileStats() // Employee stats
- handleQuickAction() // Employee actions
- updateDateTime() // Real-time clock
```

#### Admin Profile (`admin-profile.js`)
```javascript
- initializeAdminProfile()
- updateAdminStats() // System stats
- handleAdminAction() // Admin actions
- handleBackupData()
- handleAuditLog()
- logSecurityEvent()
- updateSystemStats()
```

### CSS Classes

#### Employee Profile
```css
.quick-action-btn // Standard blue theme
.stat-item .stat-number // Blue color
.profile-cover // Standard blue gradient
```

#### Admin Profile
```css
.admin-cover // Dark blue gradient theme
.admin-cover .avatar-edit-btn // Dark blue button
.quick-action-btn // Consistent with theme
```

## 🔐 Perbedaan Keamanan

### Employee Profile
- **Password Requirements**: Minimal 6 karakter
- **Security Level**: Standard
- **Access Control**: Basic user permissions

### Admin Profile
- **Password Requirements**: Minimal 8 karakter + kompleksitas
- **Security Level**: Enhanced
- **Access Control**: Full system access
- **Additional Features**:
  - 2FA support
  - Session timeout control
  - Login alerts
  - IP whitelist
  - Security event logging

## 🎯 User Experience

### Employee Profile
- **Focus**: Personal productivity & attendance
- **Navigation**: Employee-specific pages
- **Actions**: Self-service requests
- **Information**: Personal performance data

### Admin Profile
- **Focus**: System management & oversight
- **Navigation**: Admin-specific pages  
- **Actions**: System administration
- **Information**: System-wide statistics

## 📱 Responsive Behavior

### Kesamaan
- Responsive grid layout
- Mobile-friendly navigation
- Touch-optimized buttons

### Perbedaan
- Admin profile memiliki lebih banyak data
- Employee profile lebih fokus pada aksi cepat
- Admin profile memiliki kontrol keamanan tambahan

## 🔄 Navigation Flow

### Employee Flow
```
employee-dashboard.html → employee-profile.html
employee-reports.html → employee-profile.html
employee-requests.html → employee-profile.html
employee-schedule.html → employee-profile.html
```

### Admin Flow
```
admin-dashboard.html → admin-profile.html
admin-employees.html → admin-profile.html
admin-reports.html → admin-profile.html
admin-schedule.html → admin-profile.html
admin-settings.html → admin-profile.html
```

## 🎨 Design Philosophy

### Employee Profile
- **Approachable**: Friendly standard blue colors
- **Personal**: Focus on individual data
- **Efficient**: Quick access to common tasks
- **Informative**: Clear performance metrics

### Admin Profile
- **Authoritative**: Professional dark blue colors
- **Comprehensive**: System-wide overview
- **Secure**: Enhanced security controls
- **Powerful**: Advanced administrative tools
- **Consistent**: Maintains website theme integrity

## 🚀 Future Enhancements

### Employee Profile
- Performance analytics
- Goal tracking
- Team collaboration features
- Mobile app integration

### Admin Profile
- Advanced audit trails
- System health monitoring
- Bulk operations
- API management
- Advanced reporting tools

Kedua profil ini dirancang untuk memberikan pengalaman yang optimal sesuai dengan role dan tanggung jawab masing-masing user dalam sistem absensi.
